"""
LLM client implementation for OpenAI.
"""

import json
import logging
import os
from abc import ABC, abstractmethod
from typing import Dict, List

logger = logging.getLogger(__name__)


class LLMClient(ABC):
    """Abstract base class for LLM clients"""
    
    @abstractmethod
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Call the LLM with messages and return the response"""
        pass



class OpenAIClient(LLMClient):
    """OpenAI LLM client"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4o-mini"):
        try:
            from openai import OpenAI
        except ImportError:
            raise ImportError("OpenAI library not installed. Run: poetry install --extras openai")
        
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not provided and OPENAI_API_KEY environment variable not set")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
    
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Call OpenAI API"""
        # Log the input
        logger.info("🔄 LLM Input:")
        for i, msg in enumerate(messages):
            content_preview = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
            logger.info(f"  [{i}] {msg['role']}: {content_preview}")
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=500,
                temperature=0.1
            )
            content = response.choices[0].message.content
            
            # Log the output
            logger.info("🔄 LLM Output:")
            logger.info(f"  {content}")
            
            return content
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            # Fallback response
            return json.dumps({
                "reasoning": "OpenAI API call failed, using fallback",
                "action": {"type": "done"},
                "memory_update": "Error occurred during OpenAI call"
            })


def create_llm_client(api_key: str = None, model: str = "gpt-4o-mini", **kwargs) -> LLMClient:
    """Factory function to create OpenAI LLM client"""
    return OpenAIClient(api_key=api_key, model=model, **kwargs)
