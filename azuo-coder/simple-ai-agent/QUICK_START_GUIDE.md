# Quick Start Guide - Enhanced Simple AI Agent

## 🚀 Installation & Setup

```bash
# 1. Setup the project
./setup.sh

# 2. Activate environment
poetry shell

# 3. Add OpenAI API key to .env
echo "OPENAI_API_KEY=your-key-here" >> .env
```

## 📖 Basic Usage

### 1. **Simplest Usage - Command Line**

```bash
# Basic usage (requires OpenAI API key)
poetry run simple-agent --goal "Search for cat photos"

# With different models
poetry run simple-agent --goal "Buy wireless headphones" --model gpt-4
poetry run simple-agent --goal "Book a flight to Paris" --model gpt-4o-mini
```

### 2. **Python Code - Basic Usage**

```python
import os
from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client

# Set your OpenAI API key
os.environ['OPENAI_API_KEY'] = 'your-api-key-here'

# Create agent with OpenAI
goal = "Search for wireless headphones"
llm_client = create_llm_client(model="gpt-4o-mini")
agent = SimpleAIAgent(goal, llm_client)

# Run the agent
agent.run(max_steps=5)

# Check results
print(f"Final URL: {agent.state.current_page.url}")
print(f"Steps taken: {agent.state.step_count}")
print(f"Memories: {agent.state.memory}")
```

### 3. **Python Code - Advanced Models**

```python
from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client

# Use a more powerful model
llm_client = create_llm_client(model="gpt-4")

# Create and run agent
agent = SimpleAIAgent("Find laptop deals under $1000", llm_client)
agent.run(max_steps=8)
```

## 🎯 Common Use Cases

### **E-commerce Shopping**
```python
goals = [
    "Buy wireless headphones under $100",
    "Find the best rated coffee maker",
    "Compare laptop prices",
    "Add a phone case to cart"
]
```

### **Travel Booking**
```python
goals = [
    "Book a flight from NYC to Paris",
    "Find hotels in Tokyo",
    "Search for car rentals in Miami",
    "Compare flight prices to London"
]
```

### **Research & Information**
```python
goals = [
    "Find information about electric cars",
    "Research the latest smartphone deals",
    "Look up reviews for a specific product",
    "Find electronics section"
]
```

## 🔧 Advanced Usage

### **Step-by-Step Control**

```python
agent = SimpleAIAgent("Your goal", llm_client)

for step in range(5):
    # Show current state
    page = agent.state.current_page
    print(f"Step {step}: {page.title} - {len(page.elements)} elements")
    
    # Take one step
    is_done = agent.step()
    
    # Check what happened
    if agent.state.memory:
        print(f"AI did: {agent.state.memory[-1]}")
    
    if is_done:
        break
```

### **Monitoring & Inspection**

```python
agent = SimpleAIAgent("Your goal", llm_client)

# Before running
print(f"Starting at: {agent.state.current_page.url}")
print(f"Available elements: {len(agent.state.current_page.elements)}")

# Run agent
agent.run(max_steps=5)

# After running
print(f"Ended at: {agent.state.current_page.url}")
print(f"Conversation history: {len(agent.conversation_history)} messages")
print(f"AI's memory:")
for i, memory in enumerate(agent.state.memory, 1):
    print(f"  {i}. {memory}")
```

### **Direct Web Simulator Usage**

```python
from simple_ai_agent import WebSimulator

# Create simulator
simulator = WebSimulator()

# Get current page
page = simulator.get_current_page()
print(f"Page: {page.title}")
print(f"Elements: {len(page.elements)}")

# Interact with page
result = simulator.click_element(0)  # Click first element
result = simulator.type_text(0, "search query")  # Type in first input
result = simulator.navigate_to("https://example.com/shop")  # Navigate

# Check new state
page = simulator.get_current_page()
print(f"New page: {page.title}")
```

## 🎮 What the AI Can See and Do

### **What AI Sees**
```python
# Current page information
agent.state.current_page.url          # "https://example.com/search"
agent.state.current_page.title        # "Search Results"
agent.state.current_page.elements     # List of WebElement objects
agent.state.current_page.page_text    # Visible text content

# Each element has:
element.index        # 0, 1, 2, ... (for targeting)
element.tag          # "button", "input", "a", etc.
element.text         # "Search", "Add to Cart", etc.
element.attributes   # {"class": "btn", "href": "/shop"}
```

### **Actions AI Can Take**
```python
# Click any element by index
{"type": "click_element", "index": 5}

# Type text into input fields
{"type": "type_text", "index": 0, "text": "wireless headphones"}

# Scroll the page
{"type": "scroll", "direction": "down"}

# Navigate to URL
{"type": "navigate", "url": "https://example.com/shop"}

# Extract text from element
{"type": "extract_text", "index": 3}

# Mark task complete
{"type": "done"}
```

## 🧠 AI Reasoning Examples

### **OpenAI Response Example**
```json
{
  "reasoning": "I can see a search input field at index 0. I should click on it first to focus it, then type my search query.",
  "action": {"type": "click_element", "index": 0},
  "memory_update": "Clicked on search input to focus it"
}
```

### **Real AI Response (more sophisticated)**
```json
{
  "reasoning": "I'm on an e-commerce homepage with multiple navigation options. Since I need to find wireless headphones under $100, I should use the search functionality rather than browsing categories, as it will be more direct. I can see a search input at index 0.",
  "action": {"type": "click_element", "index": 0},
  "memory_update": "Decided to use search function for efficiency"
}
```

## 🔍 Debugging & Troubleshooting

### **Enable Debug Logging**
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or in CLI
poetry run simple-agent --goal "Your goal" --log-level debug
```

### **Check Agent State**
```python
# Current page info
print(f"URL: {agent.state.current_page.url}")
print(f"Title: {agent.state.current_page.title}")
print(f"Elements: {len(agent.state.current_page.elements)}")

# Agent memory
print(f"Steps: {agent.state.step_count}")
print(f"Memory: {agent.state.memory}")

# Conversation history
print(f"Messages: {len(agent.conversation_history)}")
```

### **Test Individual Components**
```python
# Test web simulator
from simple_ai_agent import WebSimulator
simulator = WebSimulator()
page = simulator.get_current_page()
print(f"Simulator working: {page.title}")

# Test LLM client (requires API key)
from simple_ai_agent.llm_clients import create_llm_client
client = create_llm_client(model="gpt-4o-mini")
response = client.call([{"role": "user", "content": "test"}])
print(f"LLM working: {len(response)} chars")
```

## 📚 Examples to Try

```bash
# Run comprehensive examples
poetry run python examples/usage_examples.py

# Run dynamic reasoning demo
poetry run python examples/dynamic_reasoning_demo.py

# Run real AI demo (requires API key)
poetry run python examples/real_dynamic_demo.py
```

## 🎯 Next Steps

1. **Start Simple**: Try the basic examples first
2. **Add Real AI**: Get an API key and see the difference
3. **Customize**: Modify goals and see how AI adapts
4. **Extend**: Add new page types or actions to the simulator
5. **Scale**: Replace simulator with real browser automation

Your enhanced agent now has the same core capability as browser-use: **content-aware reasoning**! 🎉
