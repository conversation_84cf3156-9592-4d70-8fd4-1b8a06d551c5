#!/usr/bin/env python3
"""
Quick Browser Test - Simple test of real browser automation.
"""

import logging
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent.browser_automation import SyncBrowserWrapper
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def main():
    """Quick test of browser automation"""
    setup_logging()
    
    # Load environment variables from .env file
    load_dotenv()
    
    print("🌐 Quick Browser Test")
    print("=" * 30)
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return
        
    # Create browser wrapper
    browser = SyncBrowserWrapper(headless=True)  # Run headless for testing
    
    try:
        print("🚀 Starting browser...")
        browser.start()
        
        print("🌐 Navigating to Google...")
        result = browser.navigate_to("https://www.google.com")
        print(f"   Result: {result}")
        
        print("📄 Getting page state...")
        page_state = browser.get_current_page_state()
        print(f"   URL: {page_state.url}")
        print(f"   Title: {page_state.title}")
        print(f"   Elements found: {len(page_state.elements)}")
        
        # Show first few elements
        print("\n🔍 First 5 elements:")
        for i, element in enumerate(page_state.elements[:5]):
            print(f"   [{i}] {element}")
            
        # Try to find and interact with search box
        search_box_index = None
        for i, element in enumerate(page_state.elements):
            if (element.tag == 'input' or element.tag == 'textarea') and (
                'search' in element.attributes.get('type', '') or
                'search' in element.attributes.get('name', '') or
                'q' in element.attributes.get('name', '') or
                'APjFqb' in element.attributes.get('id', '') or  # Google's search box ID
                'gLFyf' in element.attributes.get('class', '')):  # Google's search box class
                search_box_index = i
                break
                
        if search_box_index is not None:
            print(f"\n⌨️ Found search box at index {search_box_index}, typing 'AI agents'...")
            result = browser.type_text(search_box_index, "AI agents")
            print(f"   Result: {result}")
            
            # Try to find search button
            search_button_index = None
            page_state = browser.get_current_page_state()  # Refresh state
            for i, element in enumerate(page_state.elements):
                if (element.tag == 'button' or element.tag == 'input') and (
                    'search' in element.text.lower() or 
                    'submit' in element.attributes.get('type', '') or
                    'search' in element.attributes.get('value', '').lower()):
                    search_button_index = i
                    break
                    
            if search_button_index is not None:
                print(f"🖱️ Found search button at index {search_button_index}, clicking...")
                result = browser.click_element(search_button_index)
                print(f"   Result: {result}")
                
                # Wait a moment and get new page state
                import time
                time.sleep(3)
                
                page_state = browser.get_current_page_state()
                print(f"\n📄 After search:")
                print(f"   URL: {page_state.url}")
                print(f"   Title: {page_state.title}")
                print(f"   Page content preview: {page_state.page_text[:200]}...")
                
            else:
                print("❌ Could not find search button")
        else:
            print("❌ Could not find search box")
            
        print("\n✅ Browser test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("🛑 Stopping browser...")
        browser.stop()


if __name__ == "__main__":
    main()
