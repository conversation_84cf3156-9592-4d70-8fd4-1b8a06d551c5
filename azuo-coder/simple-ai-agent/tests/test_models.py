"""
Tests for the data models.
"""

import pytest
from simple_ai_agent.models import Action, ActionType, AgentState, WebElement, PageState


class TestActionType:
    """Test cases for ActionType enum"""

    def test_action_type_values(self):
        """Test that ActionType has expected values"""
        assert ActionType.CLICK_ELEMENT.value == "click_element"
        assert ActionType.TYPE_TEXT.value == "type_text"
        assert ActionType.SCROLL.value == "scroll"
        assert ActionType.NAVIGATE.value == "navigate"
        assert ActionType.EXTRACT_TEXT.value == "extract_text"
        assert ActionType.DONE.value == "done"


class TestAction:
    """Test cases for Action dataclass"""

    def test_action_creation(self):
        """Test creating Action instances"""
        action = Action(ActionType.CLICK_ELEMENT, index=0)
        assert action.type == ActionType.CLICK_ELEMENT
        assert action.index == 0
        assert action.text is None

    def test_action_string_representation(self):
        """Test Action string representations"""
        # Test click element action
        click_action = Action(ActionType.CLICK_ELEMENT, index=0)
        assert str(click_action) == "Click element [0]"

        # Test type text action
        type_action = Action(ActionType.TYPE_TEXT, index=0, text="hello")
        assert str(type_action) == "Type 'hello' into element [0]"

        # Test scroll action
        scroll_action = Action(ActionType.SCROLL, direction="down")
        assert str(scroll_action) == "Scroll down"

        # Test navigate action
        nav_action = Action(ActionType.NAVIGATE, url="https://example.com")
        assert str(nav_action) == "Navigate to: https://example.com"

        # Test extract text action
        extract_action = Action(ActionType.EXTRACT_TEXT, index=1)
        assert str(extract_action) == "Extract text from element [1]"

        # Test done action
        done_action = Action(ActionType.DONE)
        assert str(done_action) == "Task completed"


class TestWebElement:
    """Test cases for WebElement dataclass"""

    def test_web_element_creation(self):
        """Test creating WebElement instances"""
        element = WebElement(
            index=0,
            tag="button",
            text="Click me",
            attributes={"class": "btn", "id": "submit"}
        )

        assert element.index == 0
        assert element.tag == "button"
        assert element.text == "Click me"
        assert element.attributes["class"] == "btn"

    def test_web_element_string_representation(self):
        """Test WebElement string representation"""
        element = WebElement(
            index=1,
            tag="input",
            text=None,
            attributes={"type": "text", "placeholder": "Enter text"}
        )

        str_repr = str(element)
        assert "[1]" in str_repr
        assert "<input" in str_repr
        assert 'type="text"' in str_repr


class TestPageState:
    """Test cases for PageState dataclass"""

    def test_page_state_creation(self):
        """Test creating PageState instances"""
        elements = [
            WebElement(0, "button", "Click me"),
            WebElement(1, "input", None, {"type": "text"})
        ]

        page = PageState(
            url="https://example.com",
            title="Test Page",
            elements=elements,
            page_text="Welcome to test page"
        )

        assert page.url == "https://example.com"
        assert page.title == "Test Page"
        assert len(page.elements) == 2
        assert page.page_text == "Welcome to test page"

    def test_get_element_by_index(self):
        """Test getting element by index"""
        elements = [
            WebElement(0, "button", "Click me"),
            WebElement(1, "input", None, {"type": "text"})
        ]

        page = PageState(
            url="https://example.com",
            title="Test Page",
            elements=elements,
            page_text="Test"
        )

        element = page.get_element_by_index(1)
        assert element is not None
        assert element.tag == "input"

        # Test non-existent element
        element = page.get_element_by_index(99)
        assert element is None


class TestAgentState:
    """Test cases for AgentState dataclass"""

    def test_agent_state_creation(self):
        """Test creating AgentState instances"""
        elements = [WebElement(0, "button", "Click me")]
        page = PageState("https://example.com", "Test", elements, "Test page")

        state = AgentState(
            goal="Test goal",
            current_page=page,
            memory=[]
        )

        assert state.goal == "Test goal"
        assert state.current_page.url == "https://example.com"
        assert state.memory == []
        assert state.step_count == 0

    def test_add_memory(self):
        """Test adding memory entries"""
        elements = [WebElement(0, "button", "Click me")]
        page = PageState("https://example.com", "Test", elements, "Test page")

        state = AgentState(
            goal="Test goal",
            current_page=page,
            memory=[]
        )

        state.add_memory("First memory")
        state.add_memory("Second memory")

        assert len(state.memory) == 2
        assert state.memory[0] == "First memory"
        assert state.memory[1] == "Second memory"

    def test_get_memory_summary_empty(self):
        """Test memory summary when empty"""
        elements = [WebElement(0, "button", "Click me")]
        page = PageState("https://example.com", "Test", elements, "Test page")

        state = AgentState(
            goal="Test goal",
            current_page=page,
            memory=[]
        )

        summary = state.get_memory_summary()
        assert summary == "None"

    def test_get_memory_summary_with_entries(self):
        """Test memory summary with entries"""
        elements = [WebElement(0, "button", "Click me")]
        page = PageState("https://example.com", "Test", elements, "Test page")

        state = AgentState(
            goal="Test goal",
            current_page=page,
            memory=["First memory", "Second memory"]
        )

        summary = state.get_memory_summary()
        assert summary == "First memory; Second memory"

    def test_increment_step(self):
        """Test incrementing step counter"""
        elements = [WebElement(0, "button", "Click me")]
        page = PageState("https://example.com", "Test", elements, "Test page")

        state = AgentState(
            goal="Test goal",
            current_page=page,
            memory=[]
        )
        
        assert state.step_count == 0
        
        state.increment_step()
        assert state.step_count == 1
        
        state.increment_step()
        assert state.step_count == 2
