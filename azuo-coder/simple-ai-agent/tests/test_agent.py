"""
Tests for the SimpleAIAgent class.
"""

import os
import pytest
from unittest.mock import Mock, patch
from simple_ai_agent import SimpleA<PERSON>gent, ActionType, AgentState
from simple_ai_agent.llm_clients import create_llm_client


class MockLLMClient:
    """Mock LLM client for testing"""

    def __init__(self, responses=None):
        self.responses = responses or [
            '{"reasoning": "test reasoning", "action": {"type": "done"}, "memory_update": "test memory"}'
        ]
        self.call_count = 0

    def call(self, messages):
        if self.call_count < len(self.responses):
            response = self.responses[self.call_count]
        else:
            response = '{"reasoning": "default", "action": {"type": "done"}, "memory_update": "default"}'
        self.call_count += 1
        return response


class TestSimpleAIAgent:
    """Test cases for SimpleAIAgent"""

    def test_agent_initialization(self):
        """Test that agent initializes correctly"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        assert agent.goal == goal
        assert agent.llm_client == llm_client
        assert agent.state.goal == goal
        assert agent.state.step_count == 0
        assert len(agent.state.memory) == 0
        assert len(agent.conversation_history) == 0
    
    def test_system_prompt_generation(self):
        """Test that system prompt is generated correctly"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        prompt = agent.get_system_prompt()
        assert "AI agent" in prompt
        assert "JSON object" in prompt
        assert "reasoning" in prompt
        assert "action" in prompt
        assert "memory_update" in prompt

    def test_state_description_generation(self):
        """Test that state description is generated correctly"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        description = agent.get_current_state_description()
        assert goal in description
        assert "ExampleShop" in description
        assert "Search" in description
        assert "Step: 0" in description

    def test_action_execution_click(self):
        """Test click action execution"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        action_data = {"type": "click_element", "index": 0}
        result = agent.execute_action(action_data)

        assert "clicked" in result.lower() or "navigated" in result.lower()

    def test_action_execution_type(self):
        """Test type action execution"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        action_data = {"type": "type_text", "index": 0, "text": "hello world"}
        result = agent.execute_action(action_data)

        assert "hello world" in result
        assert "typed" in result.lower()

    def test_action_execution_navigate(self):
        """Test navigate action execution"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        action_data = {"type": "navigate", "url": "https://example.com/test"}
        result = agent.execute_action(action_data)

        assert "navigated" in result.lower()
        assert "https://example.com/test" in result

    def test_action_execution_done(self):
        """Test done action execution"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        action_data = {"type": "done"}
        result = agent.execute_action(action_data)

        assert "completed" in result.lower()
    
    def test_json_parsing_valid(self):
        """Test parsing valid JSON response"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        response = '{"reasoning": "test", "action": {"type": "done"}, "memory_update": "test"}'
        parsed = agent.parse_ai_response(response)

        assert parsed["reasoning"] == "test"
        assert parsed["action"]["type"] == "done"
        assert parsed["memory_update"] == "test"

    def test_json_parsing_with_markdown(self):
        """Test parsing JSON wrapped in markdown"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        response = '```json\n{"reasoning": "test", "action": {"type": "done"}, "memory_update": "test"}\n```'
        parsed = agent.parse_ai_response(response)

        assert parsed["reasoning"] == "test"
        assert parsed["action"]["type"] == "done"
        assert parsed["memory_update"] == "test"

    def test_json_parsing_invalid(self):
        """Test parsing invalid JSON response"""
        goal = "Test goal"
        llm_client = MockLLMClient()
        agent = SimpleAIAgent(goal, llm_client)

        response = 'invalid json'
        parsed = agent.parse_ai_response(response)

        assert "failed to parse" in parsed["reasoning"].lower()
        assert parsed["action"]["type"] == "done"

    def test_step_execution(self):
        """Test single step execution"""
        goal = "Search for cat photos"
        # Create a mock that returns a click action first, then done
        responses = [
            '{"reasoning": "I should click search", "action": {"type": "click_element", "index": 0}, "memory_update": "clicked search"}',
            '{"reasoning": "Task complete", "action": {"type": "done"}, "memory_update": "finished"}'
        ]
        llm_client = MockLLMClient(responses)
        agent = SimpleAIAgent(goal, llm_client)

        # Execute one step
        is_done = agent.step()

        # Check that state was updated
        assert agent.state.step_count == 1
        assert len(agent.state.memory) == 1
        assert len(agent.conversation_history) == 2  # assistant + user response

        # First step should not be done (it's a click action)
        assert not is_done

    def test_full_run(self):
        """Test full agent run"""
        goal = "Search for cat photos"
        # Create responses that simulate a search flow
        responses = [
            '{"reasoning": "Click search box", "action": {"type": "click_element", "index": 0}, "memory_update": "clicked search box"}',
            '{"reasoning": "Type search term", "action": {"type": "type_text", "index": 0, "text": "cat photos"}, "memory_update": "typed search term"}',
            '{"reasoning": "Submit search", "action": {"type": "click_element", "index": 1}, "memory_update": "submitted search"}',
            '{"reasoning": "Search complete", "action": {"type": "done"}, "memory_update": "search completed"}'
        ]
        llm_client = MockLLMClient(responses)
        agent = SimpleAIAgent(goal, llm_client)

        # Run the agent
        agent.run(max_steps=5)

        # Check final state
        assert agent.state.step_count > 0
        assert len(agent.state.memory) > 0
