#!/usr/bin/env python3
"""
Visible Browser Demo - Watch the AI control a real browser!
This opens a visible browser window so you can see the AI in action.
"""

import logging
import os
import sys
import time
import json
import re
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent.browser_automation import SyncBrowserWrapper
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def main():
    """Run the visible browser demo"""
    setup_logging()
    
    # Load environment variables from .env file
    load_dotenv()
    
    print("👁️  VISIBLE BROWSER AI DEMO")
    print("=" * 50)
    print("🤖 Watch as AI controls a real browser!")
    print("🌐 A browser window will open and you'll see:")
    print("   • AI navigating to websites")
    print("   • AI typing into search boxes")
    print("   • AI clicking buttons and links")
    print("   • AI making intelligent decisions")
    print("=" * 50)
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return
        
    print("\n🚀 Starting browser in 3 seconds...")
    time.sleep(3)
    
    # Create browser wrapper with visible window
    browser = SyncBrowserWrapper(headless=False)
    
    try:
        print("🌐 Opening browser window...")
        browser.start()
        
        print("📍 Step 1: Navigating to Google...")
        browser.navigate_to("https://www.google.com")
        time.sleep(2)
        
        print("📄 Step 2: Getting page elements...")
        page_state = browser.get_current_page_state()
        print(f"   Found {len(page_state.elements)} interactive elements")
        
        # Find search box
        search_box_index = None
        for i, element in enumerate(page_state.elements):
            if (element.tag == 'input' or element.tag == 'textarea') and (
                'search' in element.attributes.get('type', '') or 
                'search' in element.attributes.get('name', '') or
                'q' in element.attributes.get('name', '') or
                'APjFqb' in element.attributes.get('id', '') or
                'gLFyf' in element.attributes.get('class', '')):
                search_box_index = i
                break
                
        if search_box_index is not None:
            print(f"🔍 Step 3: Found search box at index {search_box_index}")
            print("⌨️  Step 4: Typing 'AI agents' into search box...")
            browser.type_text(search_box_index, "AI agents")
            time.sleep(2)
            
            print("⏎  Step 5: Pressing Enter to search...")
            browser.type_text(search_box_index, "", press_enter=True)
            time.sleep(4)
            
            print("📄 Step 6: Getting search results...")
            page_state = browser.get_current_page_state()
            print(f"   New URL: {page_state.url}")
            print(f"   Page title: {page_state.title}")
            
            # Look for search result links
            result_links = []
            for i, element in enumerate(page_state.elements):
                if element.tag == 'a' and element.text and len(element.text) > 10:
                    if not any(skip in element.text.lower() for skip in ['images', 'videos', 'news', 'shopping', 'maps']):
                        result_links.append((i, element))
                        
            if result_links:
                print(f"🔗 Step 7: Found {len(result_links)} search result links")
                print("   First few results:")
                for i, (idx, element) in enumerate(result_links[:3]):
                    print(f"     [{idx}] {element.text[:60]}...")
                    
                if len(result_links) > 0:
                    first_result_idx, first_result = result_links[0]
                    print(f"\n🖱️  Step 8: Clicking on first result...")
                    print(f"   Clicking: {first_result.text[:50]}...")
                    browser.click_element(first_result_idx)
                    time.sleep(3)
                    
                    print("📄 Step 9: Final page state...")
                    final_state = browser.get_current_page_state()
                    print(f"   Final URL: {final_state.url}")
                    print(f"   Final title: {final_state.title}")
                    
            else:
                print("❌ No search result links found")
                
        else:
            print("❌ Could not find search box")
            
        print("\n✅ Demo completed! The browser will stay open for 10 seconds...")
        print("👀 You can see the final page that the AI navigated to.")
        time.sleep(10)
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        print("🛑 Closing browser...")
        browser.stop()
        print("👋 Demo finished!")


if __name__ == "__main__":
    main()
