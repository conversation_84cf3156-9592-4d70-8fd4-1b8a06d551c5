#!/usr/bin/env python3
"""
Dynamic Reasoning Demo - Shows how the AI agent can reason about webpage content
and adapt its actions based on what it sees, just like browser-use.
"""

import logging
import sys
from pathlib import Path

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import SimulatedLLMClient


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def demo_task(goal: str, description: str):
    """Run a demo task and show the results"""
    print(f"\n{'='*60}")
    print(f"🎯 DEMO: {description}")
    print(f"Goal: {goal}")
    print(f"{'='*60}")
    
    # Create agent with simulated LLM
    llm_client = SimulatedLLMClient(goal)
    agent = SimpleAIAgent(goal, llm_client)
    
    # Show initial page state
    print(f"\n📄 INITIAL PAGE STATE:")
    print(f"URL: {agent.state.current_page.url}")
    print(f"Title: {agent.state.current_page.title}")
    print(f"\nAvailable Elements:")
    print(agent.state.current_page.get_elements_summary())
    
    # Run the agent
    try:
        agent.run(max_steps=6)
        
        # Show final results
        print(f"\n📊 FINAL RESULTS:")
        print(f"Final URL: {agent.state.current_page.url}")
        print(f"Steps taken: {agent.state.step_count}")
        print(f"Memory entries: {len(agent.state.memory)}")
        for i, memory in enumerate(agent.state.memory, 1):
            print(f"  {i}. {memory}")
            
    except Exception as e:
        print(f"❌ Error during demo: {e}")


def main():
    """Run multiple demos showing different capabilities"""
    setup_logging()
    
    print("🤖 Dynamic Reasoning Demo")
    print("=" * 60)
    print("This demo shows how the AI agent can:")
    print("• See and analyze real webpage content")
    print("• Reason about available elements and their purposes")
    print("• Adapt actions based on what it finds on each page")
    print("• Navigate through different types of pages")
    print("• Just like browser-use, but simplified!")
    
    # Demo 1: Search Task
    demo_task(
        "Search for cat photos",
        "Basic Search - AI sees search box and executes search"
    )
    
    # Demo 2: Flight Booking
    demo_task(
        "Book a flight to Paris",
        "Travel Booking - AI navigates to booking form and fills it out"
    )
    
    # Demo 3: Shopping
    demo_task(
        "Buy wireless headphones",
        "E-commerce - AI navigates to shop and searches for products"
    )
    
    # Demo 4: Complex Navigation
    demo_task(
        "Find electronics section",
        "Navigation - AI explores site structure to find specific section"
    )
    
    print(f"\n{'='*60}")
    print("🎉 DEMO COMPLETE!")
    print("=" * 60)
    print("\nKey Observations:")
    print("• The AI can see different page layouts and content")
    print("• It adapts its strategy based on available elements")
    print("• Each page has realistic, dynamic content")
    print("• Actions change the page state, just like real browsing")
    print("• The agent reasons about what it sees before acting")
    print("\nThis demonstrates the core capability that makes")
    print("browser-use so powerful: content-aware reasoning!")


if __name__ == "__main__":
    main()
