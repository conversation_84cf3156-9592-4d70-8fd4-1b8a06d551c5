#!/usr/bin/env python3
"""
Real Browser Demo - Uses actual browser automation with <PERSON><PERSON>.
This will open a real browser and interact with real websites!
"""

import logging
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent.browser_automation import SyncBrowserWrapper
from simple_ai_agent.llm_clients import create_llm_client
from simple_ai_agent.models import AgentState


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


class RealBrowserAgent:
    """AI Agent that uses real browser automation"""
    
    def __init__(self, goal: str, llm_client, headless: bool = False):
        self.goal = goal
        self.llm_client = llm_client
        self.browser = SyncBrowserWrapper(headless=headless)
        self.state = None
        self.step_count = 0
        self.memory = []
        self.conversation_history = []
        
    def start(self):
        """Start the browser"""
        self.browser.start()
        
    def stop(self):
        """Stop the browser"""
        self.browser.stop()
        
    def get_system_prompt(self) -> str:
        """Get the system prompt for the AI"""
        return """You are a helpful AI agent that can control a web browser to accomplish tasks.

You will be given:
1. A goal to accomplish
2. The current state of the webpage (URL, title, available elements)
3. Your memory of previous actions

You must respond with a JSON object containing:
- "reasoning": Your thought process about what to do next
- "action": The action to take (see available actions below)
- "memory_update": A brief note about what you did for future reference

Available actions:
- {"type": "click_element", "index": N} - Click on element at index N
- {"type": "type_text", "index": N, "text": "..."} - Type text into element at index N
- {"type": "navigate", "url": "..."} - Navigate to a specific URL
- {"type": "scroll", "direction": "up|down"} - Scroll the page
- {"type": "extract_text", "index": N} - Extract text from element at index N
- {"type": "done"} - Mark the task as complete

Be methodical and explain your reasoning clearly."""

    def get_current_state_description(self) -> str:
        """Get a description of the current state"""
        page_state = self.browser.get_current_page_state()
        
        description = f"""
Current State:
- Goal: {self.goal}
- Step: {self.step_count}
- URL: {page_state.url}
- Page Title: {page_state.title}
- Memory: {'; '.join(self.memory) if self.memory else 'None'}

Available Elements:
"""
        
        for element in page_state.elements[:20]:  # Limit to first 20 elements
            description += f"{element}\n"
            
        if len(page_state.elements) > 20:
            description += f"... and {len(page_state.elements) - 20} more elements\n"
            
        description += f"\nPage Content Preview:\n{page_state.page_text[:500]}..."
        
        return description
        
    def execute_action(self, action_data: dict) -> str:
        """Execute an action and return the result"""
        action_type = action_data.get("type")
        
        if action_type == "click_element":
            index = action_data.get("index", 0)
            return self.browser.click_element(index)
            
        elif action_type == "type_text":
            index = action_data.get("index", 0)
            text = action_data.get("text", "")
            return self.browser.type_text(index, text)
            
        elif action_type == "navigate":
            url = action_data.get("url", "")
            return self.browser.navigate_to(url)
            
        elif action_type == "scroll":
            direction = action_data.get("direction", "down")
            return self.browser.scroll(direction)
            
        elif action_type == "extract_text":
            index = action_data.get("index", 0)
            return self.browser.extract_text(index)
            
        elif action_type == "done":
            return "Task marked as complete"
            
        else:
            return f"Unknown action type: {action_type}"
            
    def step(self) -> bool:
        """Execute one step of the agent. Returns True if done."""
        # Get current state
        state_description = self.get_current_state_description()
        
        # Prepare messages for the AI
        messages = [
            {"role": "system", "content": self.get_system_prompt()},
            {"role": "user", "content": state_description}
        ]
        
        # Add conversation history
        messages.extend(self.conversation_history)
        
        # Call the AI
        response = self.llm_client.call(messages)
        
        # Parse the response
        try:
            import json
            import re

            # Clean up the response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()

            # Remove comments
            response = re.sub(r'//.*$', '', response, flags=re.MULTILINE)
            response = response.strip()

            # Try to fix common JSON issues
            if not response.endswith('}'):
                response += '}'

            parsed_response = json.loads(response)

        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse AI response: {e}")
            print(f"Raw response: {response}")
            # Fallback to a safe action
            parsed_response = {
                "reasoning": "Failed to parse response",
                "action": {"type": "done"},
                "memory_update": "Error occurred"
            }
            
        # Extract components
        reasoning = parsed_response.get("reasoning", "No reasoning provided")
        action = parsed_response.get("action", {"type": "done"})
        memory_update = parsed_response.get("memory_update", "")
        
        # Log the AI's reasoning
        print(f"\n🤔 AI Reasoning: {reasoning}")
        print(f"🎯 Action: {action}")
        
        # Execute the action
        result = self.execute_action(action)
        print(f"✅ Result: {result}")
        
        # Update memory
        if memory_update:
            self.memory.append(memory_update)
            
        # Update conversation history
        self.conversation_history.append({"role": "assistant", "content": response})
        self.conversation_history.append({"role": "user", "content": f"Action result: {result}"})
        
        # Increment step count
        self.step_count += 1
        
        # Check if done
        return action.get("type") == "done"
        
    def run(self, max_steps: int = 10, start_url: str = None):
        """Run the agent for up to max_steps"""
        print(f"🚀 Starting real browser agent with goal: {self.goal}")

        try:
            # Navigate to starting URL if provided
            if start_url:
                self.browser.navigate_to(start_url)

            for step in range(max_steps):
                print(f"\n{'='*60}")
                print(f"Step {step + 1}/{max_steps}")
                print(f"{'='*60}")

                is_done = self.step()

                if is_done:
                    print(f"\n🎉 Task completed in {step + 1} steps!")
                    break

                # Small delay between steps
                import time
                time.sleep(2)

            else:
                print(f"\n⏰ Reached maximum steps ({max_steps})")

        finally:
            self.stop()


def main():
    """Run the real browser demo"""
    setup_logging()
    
    # Load environment variables from .env file
    load_dotenv()
    
    print("🌐 Real Browser AI Agent Demo")
    print("=" * 50)
    print("This demo uses a REAL browser with Playwright!")
    print("You'll see an actual browser window open and the AI will control it.")
    print("=" * 50)
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return
        
    # Get goal from user
    goal = input("\n🎯 What would you like the AI to do? (or press Enter for default): ").strip()
    if not goal:
        goal = "Go to Google and search for 'AI agents'"
        
    # Ask about headless mode
    headless_input = input("\n🖥️ Run in headless mode? (y/N): ").strip().lower()
    headless = headless_input in ['y', 'yes']
    
    if not headless:
        print("\n🌐 A browser window will open shortly...")
    else:
        print("\n🌐 Running in headless mode (no visible browser)...")
        
    # Create LLM client
    llm_client = create_llm_client(model="gpt-4o-mini")
    
    # Create and run agent
    agent = RealBrowserAgent(goal, llm_client, headless=headless)
    
    try:
        # Start with Google as a good starting point
        agent.start()
        agent.run(max_steps=8, start_url="https://www.google.com")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error: {e}")
    finally:
        print("\n👋 Demo completed!")


if __name__ == "__main__":
    main()
