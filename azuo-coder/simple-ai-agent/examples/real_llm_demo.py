#!/usr/bin/env python3
"""
Real LLM demo using OpenAI or Anthropic APIs.
Requires API key - shows how the agent works with real AI.
"""

import logging
import os
import sys
from pathlib import Path

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client


def load_env_file(env_file: str = ".env") -> None:
    """Load environment variables from .env file"""
    if not os.path.exists(env_file):
        return

    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def load_env_file(env_file: str = ".env"):
    """Load environment variables from .env file"""
    env_path = Path(__file__).parent.parent / env_file
    if not env_path.exists():
        return
    
    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()


def main():
    """Run the real LLM demo"""
    setup_logging()
    
    # Load environment variables
    load_env_file()
    
    print("🤖 Real AI Agent Demo")
    print("=" * 50)
    print("This demo uses real AI APIs - API key required!")
    print("You can see how a real AI model thinks and plans.")
    print("=" * 50)
    
    # Check for API key
    has_openai = bool(os.getenv('OPENAI_API_KEY'))

    if not has_openai:
        print("❌ No OpenAI API key found!")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    print("✅ Found OpenAI API key")
    
    # Get goal from user
    print("\n" + "=" * 50)
    goal = input("Enter your goal (or press Enter for 'Search for cat photos'): ").strip()
    if not goal:
        goal = "Search for cat photos"
    
    print(f"Goal: {goal}")
    print(f"Using: OpenAI")
    print("=" * 50)
    
    try:
        # Create LLM client
        llm_client = create_llm_client(model="gpt-4o-mini")
        
        # Create and run the agent
        agent = SimpleAIAgent(goal, llm_client)
        agent.run(max_steps=8)
        
        # Show final state
        print("\n" + "=" * 50)
        print("AGENT STATE SUMMARY")
        print("=" * 50)
        state = agent.get_state()
        print(f"Goal: {state.goal}")
        print(f"Steps taken: {state.step_count}")
        print(f"Current page: {state.current_page}")
        print(f"Available elements: {', '.join(state.available_elements)}")
        print(f"Memory entries: {len(state.memory)}")
        for i, memory in enumerate(state.memory, 1):
            print(f"  {i}. {memory}")
        
        # Show conversation history summary
        history = agent.get_conversation_history()
        print(f"\nConversation turns: {len(history)}")
        
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
