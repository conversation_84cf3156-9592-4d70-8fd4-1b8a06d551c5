"""
Real browser automation using Playwright.
This replaces the WebSimulator with actual browser interactions.
"""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, ElementHandle
from .models import WebElement, PageState

logger = logging.getLogger(__name__)


class BrowserAutomation:
    """Real browser automation using Playwright"""
    
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.playwright = None
        
    async def start(self):
        """Start the browser"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=self.headless)
        self.page = await self.browser.new_page()
        
        # Set a reasonable viewport size
        await self.page.set_viewport_size({"width": 1280, "height": 720})
        
        logger.info("🌐 Browser started")
        
    async def stop(self):
        """Stop the browser"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        logger.info("🌐 Browser stopped")
        
    async def navigate_to(self, url: str) -> str:
        """Navigate to a URL"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")
            
        await self.page.goto(url)
        logger.info(f"🌐 Navigated to: {url}")
        return f"Navigated to {url}"
        
    async def get_current_page_state(self) -> PageState:
        """Get the current page state with elements"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")
            
        # Get basic page info
        url = self.page.url
        title = await self.page.title()
        
        # Get all interactive elements
        elements = await self._extract_elements()
        
        # Get page text content
        page_text = await self.page.evaluate("() => document.body.innerText")
        
        return PageState(
            url=url,
            title=title,
            elements=elements,
            page_text=page_text[:2000]  # Limit text length
        )
        
    async def _extract_elements(self) -> List[WebElement]:
        """Extract interactive elements from the page"""
        elements = []
        
        # Define selectors for interactive elements
        selectors = [
            'input[type="text"]',
            'input[type="search"]', 
            'input[type="email"]',
            'input[type="password"]',
            'textarea',
            'button',
            'a[href]',
            'select',
            '[role="button"]',
            '[onclick]'
        ]
        
        index = 0
        for selector in selectors:
            try:
                element_handles = await self.page.query_selector_all(selector)
                for handle in element_handles:
                    try:
                        # Get element properties
                        tag_name = await handle.evaluate("el => el.tagName.toLowerCase()")
                        text_content = await handle.evaluate("el => el.textContent?.trim() || el.value || el.placeholder || ''")
                        
                        # Get relevant attributes
                        attributes = {}
                        for attr in ['type', 'placeholder', 'href', 'class', 'id', 'role']:
                            value = await handle.get_attribute(attr)
                            if value:
                                attributes[attr] = value
                        
                        # Skip if element is not visible
                        is_visible = await handle.is_visible()
                        if not is_visible:
                            continue
                            
                        # Create WebElement
                        web_element = WebElement(
                            index=index,
                            tag=tag_name,
                            text=text_content[:100],  # Limit text length
                            attributes=attributes
                        )
                        
                        elements.append(web_element)
                        index += 1
                        
                        # Limit total elements to avoid overwhelming the AI
                        if index >= 50:
                            break
                            
                    except Exception as e:
                        logger.debug(f"Error extracting element: {e}")
                        continue
                        
            except Exception as e:
                logger.debug(f"Error with selector {selector}: {e}")
                continue
                
        return elements
        
    async def click_element(self, index: int) -> str:
        """Click an element by index"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")
            
        elements = await self._extract_elements()
        if index >= len(elements):
            return f"Error: Element index {index} not found"
            
        element = elements[index]
        
        try:
            # Find the element again and click it
            selectors = self._get_selectors_for_element(element)
            
            for selector in selectors:
                try:
                    await self.page.click(selector, timeout=5000)
                    logger.info(f"🖱️ Clicked element [{index}]: {element.tag}")
                    return f"Clicked {element.tag} element: {element.text}"
                except:
                    continue
                    
            return f"Error: Could not click element {index}"
            
        except Exception as e:
            logger.error(f"Error clicking element {index}: {e}")
            return f"Error clicking element: {str(e)}"
            
    async def type_text(self, index: int, text: str, press_enter: bool = False) -> str:
        """Type text into an element by index"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")

        elements = await self._extract_elements()
        if index >= len(elements):
            return f"Error: Element index {index} not found"

        element = elements[index]

        try:
            # Find the element again and type into it
            selectors = self._get_selectors_for_element(element)

            for selector in selectors:
                try:
                    await self.page.fill(selector, text, timeout=5000)

                    # Press Enter if requested
                    if press_enter:
                        await self.page.press(selector, "Enter", timeout=5000)
                        logger.info(f"⌨️ Typed '{text}' and pressed Enter in element [{index}]")
                        return f"Typed '{text}' into {element.tag} and pressed Enter"
                    else:
                        logger.info(f"⌨️ Typed into element [{index}]: {text}")
                        return f"Typed '{text}' into {element.tag}"
                except:
                    continue

            return f"Error: Could not type into element {index}"

        except Exception as e:
            logger.error(f"Error typing into element {index}: {e}")
            return f"Error typing into element: {str(e)}"
            
    async def extract_text(self, index: int) -> str:
        """Extract text from an element by index"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")
            
        elements = await self._extract_elements()
        if index >= len(elements):
            return f"Error: Element index {index} not found"
            
        element = elements[index]
        return f"Extracted text: '{element.text}'"
        
    async def scroll(self, direction: str = "down") -> str:
        """Scroll the page"""
        if not self.page:
            raise RuntimeError("Browser not started. Call start() first.")
            
        if direction.lower() == "down":
            await self.page.evaluate("window.scrollBy(0, 500)")
            return "Scrolled down"
        elif direction.lower() == "up":
            await self.page.evaluate("window.scrollBy(0, -500)")
            return "Scrolled up"
        else:
            return f"Error: Unknown scroll direction '{direction}'"
            
    def _get_selectors_for_element(self, element: WebElement) -> List[str]:
        """Generate possible CSS selectors for an element"""
        selectors = []
        
        # Try ID first (most specific)
        if 'id' in element.attributes:
            selectors.append(f"#{element.attributes['id']}")
            
        # Try class combinations
        if 'class' in element.attributes:
            classes = element.attributes['class'].split()
            for cls in classes:
                selectors.append(f"{element.tag}.{cls}")
                
        # Try attribute combinations
        if element.tag == 'input' and 'type' in element.attributes:
            selectors.append(f"input[type='{element.attributes['type']}']")
            
        if 'placeholder' in element.attributes:
            selectors.append(f"{element.tag}[placeholder='{element.attributes['placeholder']}']")
            
        # Try text content
        if element.text and len(element.text) > 0:
            selectors.append(f"{element.tag}:has-text('{element.text[:20]}')")
            
        # Fallback to tag name
        selectors.append(element.tag)
        
        return selectors


class SyncBrowserWrapper:
    """Synchronous wrapper for the async browser automation"""
    
    def __init__(self, headless: bool = False):
        self.browser_automation = BrowserAutomation(headless)
        self.loop = None
        
    def start(self):
        """Start the browser"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.loop.run_until_complete(self.browser_automation.start())
        
    def stop(self):
        """Stop the browser"""
        if self.loop:
            self.loop.run_until_complete(self.browser_automation.stop())
            self.loop.close()
            
    def navigate_to(self, url: str) -> str:
        """Navigate to a URL"""
        return self.loop.run_until_complete(self.browser_automation.navigate_to(url))
        
    def get_current_page_state(self) -> PageState:
        """Get the current page state"""
        return self.loop.run_until_complete(self.browser_automation.get_current_page_state())
        
    def click_element(self, index: int) -> str:
        """Click an element by index"""
        return self.loop.run_until_complete(self.browser_automation.click_element(index))
        
    def type_text(self, index: int, text: str, press_enter: bool = False) -> str:
        """Type text into an element"""
        return self.loop.run_until_complete(self.browser_automation.type_text(index, text, press_enter))
        
    def extract_text(self, index: int) -> str:
        """Extract text from an element"""
        return self.loop.run_until_complete(self.browser_automation.extract_text(index))
        
    def scroll(self, direction: str = "down") -> str:
        """Scroll the page"""
        return self.loop.run_until_complete(self.browser_automation.scroll(direction))
