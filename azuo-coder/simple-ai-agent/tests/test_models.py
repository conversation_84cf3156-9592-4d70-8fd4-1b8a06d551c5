"""
Tests for the data models.
"""

import pytest
from simple_ai_agent.models import Action, ActionType, AgentState


class TestActionType:
    """Test cases for ActionType enum"""
    
    def test_action_type_values(self):
        """Test that ActionType has expected values"""
        assert ActionType.SEARCH.value == "search"
        assert ActionType.CLICK.value == "click"
        assert ActionType.TYPE.value == "type"
        assert ActionType.DONE.value == "done"


class TestAction:
    """Test cases for Action dataclass"""
    
    def test_action_creation(self):
        """Test creating Action instances"""
        action = Action(ActionType.CLICK, target="button")
        assert action.type == ActionType.CLICK
        assert action.target == "button"
        assert action.text is None
    
    def test_action_string_representation(self):
        """Test Action string representations"""
        # Test search action
        search_action = Action(ActionType.SEARCH, text="cat photos")
        assert str(search_action) == "Search for: cat photos"
        
        # Test click action
        click_action = Action(ActionType.CLICK, target="button")
        assert str(click_action) == "Click on: button"
        
        # Test type action
        type_action = Action(ActionType.TYPE, text="hello")
        assert str(type_action) == "Type: hello"
        
        # Test done action
        done_action = Action(ActionType.DONE)
        assert str(done_action) == "Task completed"


class TestAgentState:
    """Test cases for AgentState dataclass"""
    
    def test_agent_state_creation(self):
        """Test creating AgentState instances"""
        state = AgentState(
            goal="Test goal",
            current_page="homepage",
            available_elements=["button1", "button2"],
            memory=[]
        )
        
        assert state.goal == "Test goal"
        assert state.current_page == "homepage"
        assert state.available_elements == ["button1", "button2"]
        assert state.memory == []
        assert state.step_count == 0
    
    def test_add_memory(self):
        """Test adding memory entries"""
        state = AgentState(
            goal="Test goal",
            current_page="homepage",
            available_elements=[],
            memory=[]
        )
        
        state.add_memory("First memory")
        state.add_memory("Second memory")
        
        assert len(state.memory) == 2
        assert state.memory[0] == "First memory"
        assert state.memory[1] == "Second memory"
    
    def test_get_memory_summary_empty(self):
        """Test memory summary when empty"""
        state = AgentState(
            goal="Test goal",
            current_page="homepage",
            available_elements=[],
            memory=[]
        )
        
        summary = state.get_memory_summary()
        assert summary == "None"
    
    def test_get_memory_summary_with_entries(self):
        """Test memory summary with entries"""
        state = AgentState(
            goal="Test goal",
            current_page="homepage",
            available_elements=[],
            memory=["First memory", "Second memory"]
        )
        
        summary = state.get_memory_summary()
        assert summary == "First memory; Second memory"
    
    def test_increment_step(self):
        """Test incrementing step counter"""
        state = AgentState(
            goal="Test goal",
            current_page="homepage",
            available_elements=[],
            memory=[]
        )
        
        assert state.step_count == 0
        
        state.increment_step()
        assert state.step_count == 1
        
        state.increment_step()
        assert state.step_count == 2
