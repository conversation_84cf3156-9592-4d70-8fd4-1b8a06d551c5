"""
Command-line interface for the Simple AI Agent.
"""

import argparse
import logging
import os
import sys
from typing import Optional

from .agent import SimpleAIAgent
from .llm_clients import create_llm_client


def setup_logging(level: str = "INFO") -> None:
    """Setup logging configuration"""
    log_level = getattr(logging, level.upper(), logging.INFO)
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def load_env_file(env_file: str = ".env") -> None:
    """Load environment variables from .env file"""
    if not os.path.exists(env_file):
        return
    
    with open(env_file, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()


def main() -> None:
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Simple AI Agent - Demonstrating AI planning and reasoning",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  simple-agent --goal "Search for cat photos"
  simple-agent --goal "Book a flight" --model gpt-4
  simple-agent --goal "Find a recipe" --max-steps 5
  simple-agent --goal "Learn Python" --log-level debug
        """
    )
    
    parser.add_argument(
        "--goal",
        type=str,
        default="Search for cat photos",
        help="The goal for the AI agent to accomplish (default: 'Search for cat photos')"
    )

    parser.add_argument(
        "--max-steps",
        type=int,
        default=10,
        help="Maximum number of steps to take (default: 10)"
    )

    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default="info",
        help="Logging level (default: info)"
    )

    parser.add_argument(
        "--model",
        type=str,
        default="gpt-4o-mini",
        help="OpenAI model to use (default: gpt-4o-mini)"
    )
    
    parser.add_argument(
        "--env-file",
        type=str,
        default=".env",
        help="Path to .env file (default: .env)"
    )
    
    args = parser.parse_args()
    
    # Load environment variables
    load_env_file(args.env_file)
    
    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # Print banner
    print("🤖 Simple AI Agent")
    print("=" * 50)
    print(f"Goal: {args.goal}")
    print(f"Model: {args.model}")
    print(f"Max Steps: {args.max_steps}")
    print("=" * 50)

    try:
        # Check for API key
        if not os.getenv('OPENAI_API_KEY'):
            print("❌ Error: OPENAI_API_KEY environment variable not set")
            print("Please set your OpenAI API key in the .env file or environment")
            sys.exit(1)

        # Create LLM client
        llm_client = create_llm_client(model=args.model)

        # Create and run agent
        agent = SimpleAIAgent(args.goal, llm_client)
        agent.run(max_steps=args.max_steps)
        
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
