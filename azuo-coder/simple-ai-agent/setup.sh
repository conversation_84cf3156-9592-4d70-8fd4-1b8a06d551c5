#!/bin/bash

# Simple AI Agent Setup Script
# This script sets up the development environment using Poetry

set -e  # Exit on any error

echo "🚀 Setting up Simple AI Agent project..."

# Check if Poetry is installed
if ! command -v poetry &> /dev/null; then
    echo "❌ Poetry is not installed. Please install Poetry first:"
    echo "   curl -sSL https://install.python-poetry.org | python3 -"
    echo "   Or visit: https://python-poetry.org/docs/#installation"
    exit 1
fi

echo "✅ Poetry found: $(poetry --version)"

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Install dependencies
echo "📦 Installing dependencies..."
poetry install

# OpenAI is now included by default
echo ""
echo "✅ OpenAI dependencies installed with the main package."

# Create .env template if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env template..."
    cat > .env << EOF
# OpenAI API Key (required)
# OPENAI_API_KEY=your-openai-api-key-here

# Logging level (debug, info, warning, error)
LOG_LEVEL=info
EOF
    echo "✅ Created .env template. Please add your OpenAI API key to use the agent."
else
    echo "✅ .env file already exists."
fi

# Show virtual environment info
echo ""
echo "🎉 Setup complete!"
echo ""
echo "📍 Virtual environment location:"
poetry env info --path
echo ""
echo "🔧 To activate the virtual environment:"
echo "   poetry shell"
echo ""
echo "🏃 To run the examples:"
echo "   poetry run python examples/simple_demo.py"
echo "   poetry run python examples/real_llm_demo.py"
echo ""
echo "🛠️  To run with the CLI:"
echo "   poetry run simple-agent --help"
echo ""
echo "📚 Don't forget to:"
echo "   1. Add your OpenAI API key to .env file"
echo "   2. Read the README.md for usage instructions"
echo ""
echo "Happy coding! 🎯"
