# Simple AI Agent

A simplified AI agent that demonstrates browser-use-like capabilities with OpenAI. Learn how AI agents work by seeing them analyze webpage content, reason about actions, and execute tasks step-by-step.

## 🎯 What This Demonstrates

This project shows the essential patterns from complex AI agent systems like browser-use:

- **Content-Aware Reasoning**: AI sees webpage elements and adapts its actions
- **Structured Decision Making**: AI returns JSON responses with reasoning and actions
- **Dynamic Navigation**: AI navigates between different page types (homepage, search, products)
- **Memory & Context**: AI remembers what it has done and builds on previous actions
- **Element-Level Precision**: AI can target specific buttons, inputs, and links by index

## 🚀 Quick Start

### Prerequisites
- Python 3.9+ and Poetry
- OpenAI API key

### Setup
```bash
# 1. Setup the project
./setup.sh

# 2. Activate environment
poetry shell

# 3. Add your OpenAI API key
echo "OPENAI_API_KEY=your-key-here" >> .env
```

### Basic Usage

**Command Line:**
```bash
# Simple usage
simple-agent --goal "Search for cat photos"

# With different models
simple-agent --goal "Buy wireless headphones" --model gpt-4
```

**Python Code:**
```python
from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client

# Create agent
llm_client = create_llm_client(model="gpt-4o-mini")
agent = SimpleAIAgent("Search for wireless headphones", llm_client)

# Run the agent
agent.run(max_steps=5)

# Check results
print(f"Final URL: {agent.state.current_page.url}")
print(f"Steps taken: {agent.state.step_count}")
print(f"AI Memory: {agent.state.memory}")
```

## 🎮 What the AI Can See and Do

### AI Input (Real Example)
```
Current URL: https://example.com
Page Title: ExampleShop - Home
Available Elements:
[0] <input text="Search for anything..." type="search">
[1] <button text="Search" class="search-btn">
[2] <a text="Sign In" href="/login">
[3] <a text="Shop Now" href="/shop">
[4] <a text="Electronics" href="/electronics">

Page Content:
Welcome to ExampleShop - Your One-Stop Destination
Featured Categories: Electronics, Clothing, Travel...
```

### AI Output
```json
{
  "reasoning": "I need to search for wireless headphones. I can see a search input at index 0. Let me click on it first to focus it.",
  "action": {"type": "click_element", "index": 0},
  "memory_update": "Clicked on search input to start searching"
}
```

### Available Actions
- `click_element` - Click any element by index
- `type_text` - Type into input fields
- `scroll` - Scroll the page up/down
- `navigate` - Go to a specific URL
- `extract_text` - Get text from elements
- `done` - Mark task as complete

## 🔧 Advanced Usage

### Step-by-Step Control
```python
agent = SimpleAIAgent("Your goal", llm_client)

for step in range(5):
    print(f"Step {step}: {agent.state.current_page.title}")
    is_done = agent.step()  # Take one step
    if is_done:
        break
```

### Direct Web Simulator
```python
from simple_ai_agent import WebSimulator

simulator = WebSimulator()
page = simulator.get_current_page()
result = simulator.click_element(0)  # Click first element
result = simulator.type_text(0, "search query")  # Type in input
```

### Monitoring & Debugging
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Check agent state
print(f"Current URL: {agent.state.current_page.url}")
print(f"Elements: {len(agent.state.current_page.elements)}")
print(f"Memory: {agent.state.memory}")
```

## 🎯 Example Use Cases

**E-commerce Shopping:**
- "Buy wireless headphones under $100"
- "Find the best rated coffee maker"
- "Compare laptop prices"

**Travel Booking:**
- "Book a flight from NYC to Paris"
- "Find hotels in Tokyo"
- "Search for car rentals"

**Research & Information:**
- "Find information about electric cars"
- "Research smartphone deals"
- "Look up product reviews"

## 🆚 vs Browser-Use

| **Capability** | **Browser-Use** | **Simple AI Agent** |
|----------------|-----------------|---------------------|
| Content Awareness | ✅ Real screenshots + DOM | ✅ Simulated realistic content |
| Dynamic Elements | ✅ Any webpage | ✅ Homepage, search, products, forms |
| Learning Curve | ❌ Complex setup | ✅ Simple, educational |
| Real Browsers | ✅ Playwright automation | ❌ Web simulator only |

## 🔧 Development

```bash
# Install dev dependencies
poetry install --with dev

# Run tests
poetry run pytest

# Format code
poetry run black src/ examples/ tests/
```

## 📄 License

MIT License - see LICENSE file for details.

---

**🎉 You now have a browser-use-like agent that's perfect for learning how AI agents work!**

Inspired by [browser-use](https://github.com/gregpr07/browser-use) - simplified for education.
