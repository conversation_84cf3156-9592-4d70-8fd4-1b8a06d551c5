#!/usr/bin/env python3
"""
Real Dynamic Demo - Shows the enhanced agent with real AI reasoning.
This demonstrates how a real LLM can analyze webpage content and adapt its actions.
"""

import logging
import os
import sys
from pathlib import Path

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def load_env_file(env_file: str = ".env"):
    """Load environment variables from .env file"""
    env_path = Path(__file__).parent.parent / env_file
    if not env_path.exists():
        return
    
    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()


def demo_with_real_ai(goal: str, description: str):
    """Run a demo with real AI and show the adaptive reasoning"""
    print(f"\n{'='*70}")
    print(f"🤖 REAL AI DEMO: {description}")
    print(f"Goal: {goal}")
    print(f"{'='*70}")
    
    # Check for API keys
    has_openai = bool(os.getenv('OPENAI_API_KEY'))

    
    if not has_openai:
        print("❌ No OpenAI API key found!")
        print("To see real AI reasoning, add your API key to .env file:")
        print("  OPENAI_API_KEY=your-key-here")
        return
    else:
        # Use OpenAI
        print("✅ Using OpenAI for real AI reasoning")
        llm_client = create_llm_client(model="gpt-4o-mini")
    
    # Create and run agent
    agent = SimpleAIAgent(goal, llm_client)
    
    # Show initial page analysis
    print(f"\n📄 INITIAL PAGE ANALYSIS:")
    print(f"URL: {agent.state.current_page.url}")
    print(f"Title: {agent.state.current_page.title}")
    print(f"\nPage Elements (AI can see these):")
    for element in agent.state.current_page.elements[:5]:  # Show first 5
        print(f"  {element}")
    if len(agent.state.current_page.elements) > 5:
        print(f"  ... and {len(agent.state.current_page.elements) - 5} more elements")
    
    print(f"\nPage Content Preview:")
    content_lines = agent.state.current_page.page_text.split('\n')[:5]
    for line in content_lines:
        if line.strip():
            print(f"  {line.strip()}")
    
    # Run the agent
    try:
        print(f"\n🚀 STARTING AI AGENT...")
        agent.run(max_steps=5)
        
        # Show results
        print(f"\n📊 RESULTS:")
        print(f"Final URL: {agent.state.current_page.url}")
        print(f"Steps taken: {agent.state.step_count}")
        print(f"AI's memory of what it did:")
        for i, memory in enumerate(agent.state.memory, 1):
            print(f"  {i}. {memory}")
            
    except Exception as e:
        print(f"❌ Error during demo: {e}")


def main():
    """Run the real AI demo"""
    setup_logging()
    load_env_file()
    
    print("🤖 Enhanced Simple AI Agent - Real AI Demo")
    print("=" * 70)
    print("This demo shows how REAL AI can analyze webpage content")
    print("and adapt its actions, just like browser-use!")
    print("=" * 70)
    
    # Demo 1: Complex shopping task
    demo_with_real_ai(
        "I want to buy wireless headphones under $100 with good reviews",
        "Complex E-commerce Task"
    )
    
    # Demo 2: Travel planning
    demo_with_real_ai(
        "Find flights from San Francisco to Tokyo for next month",
        "Travel Planning Task"
    )
    
    # Demo 3: Research task
    demo_with_real_ai(
        "Find information about the latest smartphone deals",
        "Research and Information Gathering"
    )
    
    print(f"\n{'='*70}")
    print("🎉 REAL AI DEMO COMPLETE!")
    print("=" * 70)
    print("\nKey Observations:")
    print("• Real AI provides much more nuanced reasoning")
    print("• AI adapts its strategy based on what it actually sees")
    print("• Each response is unique and contextual")
    print("• AI can handle complex, multi-step goals")
    print("• The agent truly 'understands' webpage content")
    print("\nThis is the same core capability that makes browser-use")
    print("so powerful for real-world web automation!")


if __name__ == "__main__":
    main()
