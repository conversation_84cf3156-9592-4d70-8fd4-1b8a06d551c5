#!/usr/bin/env python3
"""
Simple AI Agent Demo

This demonstrates the core functionality of the AI agent:
- Web simulation mode (for testing/development)
- Real browser automation mode (for production use)
"""

import logging
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def demo_simulation_mode():
    """Demo using web simulation (no real browser)"""
    print("\n" + "="*60)
    print("🎭 SIMULATION MODE DEMO")
    print("="*60)
    print("Using simulated web content for testing and development")
    
    # Create LLM client
    llm_client = create_llm_client(model="gpt-4o-mini")
    
    # Create agent in simulation mode
    agent = SimpleAIAgent(
        goal="Search for cat photos and add one to cart",
        llm_client=llm_client,
        use_browser=False  # Use simulation
    )
    
    print(f"🎯 Goal: {agent.goal}")
    print("🎭 Using web simulation (fake websites)")
    
    try:
        # Run the agent for a few steps
        for step in range(5):
            print(f"\n--- Step {step + 1} ---")
            is_done = agent.step()
            if is_done:
                print("✅ Task completed!")
                break
        else:
            print("⏰ Reached maximum steps")
            
    except Exception as e:
        print(f"❌ Error: {e}")


def demo_browser_mode():
    """Demo using real browser automation"""
    print("\n" + "="*60)
    print("🌐 REAL BROWSER MODE DEMO")
    print("="*60)
    print("Using real browser automation with Playwright")
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return
    
    # Ask user about headless mode
    headless_input = input("Run in headless mode? (y/N): ").strip().lower()
    headless = headless_input in ['y', 'yes']
    
    if not headless:
        print("🌐 A browser window will open...")
    else:
        print("🌐 Running in headless mode...")
    
    # Create LLM client
    llm_client = create_llm_client(model="gpt-4o-mini")
    
    # Create agent in browser mode
    agent = SimpleAIAgent(
        goal="Search for 'AI agents' on Google",
        llm_client=llm_client,
        use_browser=True,  # Use real browser
        headless=headless
    )
    
    print(f"🎯 Goal: {agent.goal}")
    print("🌐 Using real browser automation")
    
    try:
        # Start browser
        agent.start_browser("https://www.google.com")
        
        # Run the agent for a few steps
        for step in range(6):
            print(f"\n--- Step {step + 1} ---")
            is_done = agent.step()
            if is_done:
                print("✅ Task completed!")
                break
            
            # Small delay between steps
            import time
            time.sleep(2)
        else:
            print("⏰ Reached maximum steps")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Always stop the browser
        agent.stop_browser()


def main():
    """Main demo function"""
    setup_logging()
    
    # Load environment variables from .env file
    load_dotenv()
    
    print("🤖 Simple AI Agent Demo")
    print("=" * 50)
    print("This agent can work in two modes:")
    print("1. Simulation mode - Uses fake web content (good for testing)")
    print("2. Browser mode - Controls real browsers (production ready)")
    print("=" * 50)
    
    while True:
        print("\nChoose demo mode:")
        print("1. Simulation mode (fast, no browser needed)")
        print("2. Real browser mode (requires browser, more realistic)")
        print("3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            demo_simulation_mode()
        elif choice == "2":
            demo_browser_mode()
        elif choice == "3":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")


if __name__ == "__main__":
    main()
