#!/usr/bin/env python3
"""
Usage Examples - Complete guide showing how to use the Enhanced Simple AI Agent

This file demonstrates all the ways you can use your browser-use-like AI agent:
1. Basic usage with OpenAI
2. Custom goals and scenarios
3. Programmatic control and monitoring
4. Advanced features and customization
"""

import logging
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent, WebSimulator
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging(level="INFO"):
    """Setup logging to see what the agent is doing"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )


# =============================================================================
# EXAMPLE 1: Basic Usage with Simulated AI (No API Key Required)
# =============================================================================

def example_1_basic_usage():
    """Basic usage with OpenAI"""
    print("\n" + "="*60)
    print("📚 EXAMPLE 1: Basic Usage (OpenAI)")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    # Create an agent with OpenAI
    goal = "Search for wireless headphones"
    llm_client = create_llm_client(model="gpt-4o-mini")
    agent = SimpleAIAgent(goal, llm_client)

    print(f"🎯 Goal: {goal}")
    print(f"🤖 AI Type: OpenAI GPT-4o-mini")

    # Run the agent
    agent.run(max_steps=5)

    # Access the results
    print(f"\n📊 Results:")
    print(f"   Final URL: {agent.state.current_page.url}")
    print(f"   Steps taken: {agent.state.step_count}")
    print(f"   Memory entries: {len(agent.state.memory)}")


# =============================================================================
# EXAMPLE 2: Advanced OpenAI Usage
# =============================================================================

def example_2_advanced_ai():
    """Using different OpenAI models for more intelligent responses"""
    print("\n" + "="*60)
    print("🧠 EXAMPLE 2: Advanced OpenAI Usage")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    # Create OpenAI client with a more powerful model
    print("✅ Using OpenAI GPT-4")
    llm_client = create_llm_client(model="gpt-4")
    
    # Create agent with real AI
    goal = "Find and compare laptop prices under $1000"
    agent = SimpleAIAgent(goal, llm_client)
    
    print(f"🎯 Goal: {goal}")
    print("🚀 Running with real AI reasoning...")
    
    # Run the agent
    agent.run(max_steps=6)


# =============================================================================
# EXAMPLE 3: Step-by-Step Control
# =============================================================================

def example_3_step_by_step():
    """Manual control - run one step at a time"""
    print("\n" + "="*60)
    print("🎮 EXAMPLE 3: Step-by-Step Control")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    goal = "Book a flight to Paris"
    llm_client = create_llm_client(model="gpt-4o-mini")
    agent = SimpleAIAgent(goal, llm_client)

    print(f"🎯 Goal: {goal}")
    print("🔍 Running step-by-step with analysis...")
    
    for step_num in range(1, 5):
        print(f"\n--- STEP {step_num} ---")
        
        # Show current page state
        page = agent.state.current_page
        print(f"📄 Current page: {page.title} ({page.url})")
        print(f"🔢 Available elements: {len(page.elements)}")
        
        # Take one step
        is_done = agent.step()
        
        # Show what happened
        if agent.state.memory:
            print(f"🧠 Latest memory: {agent.state.memory[-1]}")
        
        if is_done:
            print("✅ Task completed!")
            break
    
    print(f"\n📊 Final state: {agent.state.step_count} steps taken")


# =============================================================================
# EXAMPLE 4: Monitoring and Inspection
# =============================================================================

def example_4_monitoring():
    """Monitor the agent's decision-making process"""
    print("\n" + "="*60)
    print("🔍 EXAMPLE 4: Monitoring and Inspection")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    goal = "Find electronics section"
    llm_client = create_llm_client(model="gpt-4o-mini")
    agent = SimpleAIAgent(goal, llm_client)
    
    print(f"🎯 Goal: {goal}")
    
    # Custom monitoring function
    def monitor_step():
        page = agent.state.current_page
        print(f"\n📊 MONITORING:")
        print(f"   URL: {page.url}")
        print(f"   Elements: {len(page.elements)}")
        print(f"   Memory entries: {len(agent.state.memory)}")
        
        # Show some elements the AI can see
        print(f"   Key elements AI can see:")
        for i, element in enumerate(page.elements[:3]):
            print(f"     [{element.index}] {element.tag}: {element.text}")
    
    # Run with monitoring
    monitor_step()
    
    for step in range(3):
        print(f"\n🚀 Taking step {step + 1}...")
        is_done = agent.step()
        monitor_step()
        
        if is_done:
            break
    
    # Show conversation history
    print(f"\n💬 Conversation history: {len(agent.conversation_history)} messages")


# =============================================================================
# EXAMPLE 5: Custom Scenarios
# =============================================================================

def example_5_custom_scenarios():
    """Create custom scenarios for different use cases"""
    print("\n" + "="*60)
    print("🎨 EXAMPLE 5: Custom Scenarios")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    scenarios = [
        ("Research task", "Find information about electric cars"),
        ("Shopping task", "Buy a coffee maker with good reviews"),
        ("Travel task", "Find cheap flights to Japan"),
        ("Navigation task", "Explore the clothing section"),
    ]
    
    for scenario_name, goal in scenarios:
        print(f"\n🎭 Scenario: {scenario_name}")
        print(f"🎯 Goal: {goal}")
        
        # Create agent for this scenario
        llm_client = create_llm_client(model="gpt-4o-mini")
        agent = SimpleAIAgent(goal, llm_client)
        
        # Run quickly (fewer steps for demo)
        agent.run(max_steps=3)
        
        # Show key results
        final_url = agent.state.current_page.url
        memory_count = len(agent.state.memory)
        print(f"   ✅ Ended at: {final_url}")
        print(f"   🧠 Memories: {memory_count}")


# =============================================================================
# EXAMPLE 6: Direct Web Simulator Usage
# =============================================================================

def example_6_web_simulator():
    """Use the web simulator directly for testing"""
    print("\n" + "="*60)
    print("🌐 EXAMPLE 6: Direct Web Simulator Usage")
    print("="*60)
    
    # Create web simulator
    simulator = WebSimulator()
    
    print("🏠 Starting at homepage...")
    page = simulator.get_current_page()
    print(f"   URL: {page.url}")
    print(f"   Title: {page.title}")
    print(f"   Elements: {len(page.elements)}")
    
    # Simulate user actions
    print("\n🖱️  Clicking 'Shop Now' button...")
    result = simulator.click_element(3)  # Shop Now button
    print(f"   Result: {result}")
    
    print("\n📄 New page state:")
    page = simulator.get_current_page()
    print(f"   URL: {page.url}")
    print(f"   Title: {page.title}")
    
    # Type in search box
    print("\n⌨️  Typing 'laptop' in search box...")
    result = simulator.type_text(0, "laptop")
    print(f"   Result: {result}")
    
    # Click search button
    print("\n🔍 Clicking search button...")
    result = simulator.click_element(1)
    print(f"   Result: {result}")
    
    # Show final state
    page = simulator.get_current_page()
    print(f"\n📊 Final state:")
    print(f"   URL: {page.url}")
    print(f"   Products found: {len([e for e in page.elements if 'product' in e.attributes.get('class', '')])}")


# =============================================================================
# EXAMPLE 7: Error Handling and Edge Cases
# =============================================================================

def example_7_error_handling():
    """Handle errors and edge cases gracefully"""
    print("\n" + "="*60)
    print("⚠️  EXAMPLE 7: Error Handling")
    print("="*60)

    # Load environment variables from .env file
    load_dotenv()

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    goal = "Test error handling"
    llm_client = create_llm_client(model="gpt-4o-mini")
    agent = SimpleAIAgent(goal, llm_client)
    
    # Test invalid actions
    print("🧪 Testing invalid actions...")
    
    try:
        # Try to click non-existent element
        result = agent.execute_action({"type": "click_element", "index": 999})
        print(f"   Invalid element click: {result}")
        
        # Try to type into wrong element type
        result = agent.execute_action({"type": "type_text", "index": 1, "text": "test"})
        print(f"   Type into button: {result}")
        
        # Try unknown action
        result = agent.execute_action({"type": "unknown_action"})
        print(f"   Unknown action: {result}")
        
    except Exception as e:
        print(f"   Exception caught: {e}")
    
    print("✅ Error handling working correctly!")


# =============================================================================
# MAIN FUNCTION - Run All Examples
# =============================================================================

def main():
    """Run all usage examples"""
    print("🤖 Enhanced Simple AI Agent - Usage Examples")
    print("=" * 60)
    print("This demonstrates all the ways to use your browser-use-like agent!")
    
    # Setup logging
    setup_logging("INFO")
    
    # Load environment variables from .env file
    load_dotenv()
    
    # Run all examples
    try:
        example_1_basic_usage()
        example_2_advanced_ai()
        example_3_step_by_step()
        example_4_monitoring()
        example_5_custom_scenarios()
        example_6_web_simulator()
        example_7_error_handling()
        
    except KeyboardInterrupt:
        print("\n🛑 Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
    
    print("\n" + "="*60)
    print("🎉 ALL EXAMPLES COMPLETE!")
    print("="*60)
    print("\nKey Takeaways:")
    print("• Your agent can handle complex, multi-step tasks")
    print("• It adapts its behavior based on webpage content")
    print("• You can use it with different OpenAI models")
    print("• It provides detailed monitoring and control")
    print("• Error handling keeps it robust")
    print("\nYour mini agent now has browser-use capabilities! 🎯")


if __name__ == "__main__":
    main()
