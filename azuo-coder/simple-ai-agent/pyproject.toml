[tool.poetry]
name = "simple-ai-agent"
version = "0.1.0"
description = "A simplified AI agent demonstrating planning and reasoning concepts"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "simple_ai_agent", from = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
openai = "^1.0.0"
python-dotenv = "^1.0.0"
playwright = "^1.40.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
black = "^23.0.0"
isort = "^5.0.0"
flake8 = "^6.0.0"

[tool.poetry.scripts]
simple-agent = "simple_ai_agent.cli:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 100
