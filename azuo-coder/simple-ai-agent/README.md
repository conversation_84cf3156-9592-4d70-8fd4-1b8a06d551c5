# Simple AI Agent

A simplified AI agent project that demonstrates the core concepts of using AI for planning and reasoning, without the complexity of browser automation.

## 🎯 What This Demonstrates

This project shows the essential patterns from complex AI agent systems like browser-use, but in a much more understandable way:

1. **AI Planning**: The AI receives a goal and current state, then decides what action to take next
2. **Structured Output**: The AI returns structured JSON responses that the agent can parse and execute
3. **Memory**: The agent maintains memory of what it has done
4. **Step-by-step Execution**: The agent breaks down complex tasks into simple steps
5. **Comprehensive Logging**: Clear logging shows the input/output flow between the agent and AI

## 🚀 Quick Start

### Prerequisites

- Python 3.9 or higher
- Poetry (for dependency management)

### Setup

1. **Clone or download this project**

2. **Run the setup script:**
   ```bash
   ./setup.sh
   ```
   This will:
   - Install Poetry dependencies
   - Create a virtual environment
   - Set up optional LLM dependencies
   - Create a `.env` template

3. **Add your API keys (optional, for real LLM usage):**
   ```bash
   # Edit .env file
   OPENAI_API_KEY=your-openai-api-key-here
   # OR
   ANTHROPIC_API_KEY=your-anthropic-api-key-here
   ```

### Running Examples

```bash
# Activate the virtual environment
poetry shell

# Run the simulated demo (no API key needed)
python examples/simple_demo.py

# Run with real LLM (requires API key)
python examples/real_llm_demo.py

# Use the CLI
simple-agent --help
simple-agent --goal "Search for cat photos" --mode simulate
simple-agent --goal "Book a flight" --mode openai
```

## 📁 Project Structure

```
simple-ai-agent/
├── src/simple_ai_agent/          # Main package
│   ├── __init__.py
│   ├── agent.py                  # Core agent logic
│   ├── models.py                 # Data models
│   ├── llm_clients.py           # LLM API clients
│   └── cli.py                   # Command-line interface
├── examples/                     # Example scripts
│   ├── simple_demo.py           # Simulated AI demo
│   └── real_llm_demo.py         # Real LLM demo
├── tests/                       # Unit tests
├── pyproject.toml              # Poetry configuration
├── setup.sh                   # Setup script
├── .env                       # Environment variables (created by setup)
└── README.md                  # This file
```

## 🔧 Development

### Install development dependencies
```bash
poetry install --with dev
```

### Run tests
```bash
poetry run pytest
```

### Format code
```bash
poetry run black src/ examples/ tests/
poetry run isort src/ examples/ tests/
```

### Lint code
```bash
poetry run flake8 src/ examples/ tests/
```

## 🆚 Comparison with Complex Systems

| **Complex (browser-use)** | **Simple (this project)** |
|---------------------------|---------------------------|
| 1000+ lines across many files | ~300 lines total |
| Browser automation with Playwright | Simulated interactions |
| Dynamic action registration | Simple enum of actions |
| Complex Pydantic models | Basic dataclasses |
| Multi-layered architecture | Single-file clarity |

## 🧠 Core Concepts

### The AI Agent Loop
```
1. Prepare Context → 2. Call AI → 3. Parse Response → 4. Execute Action → 5. Update State → Repeat
```

### Example Input/Output

**Input to AI:**
```
System: You are a helpful AI agent...
User: Goal: Search for cat photos
      Current Page: homepage
      Available Elements: search_box, login_button, menu
      Memory: None
```

**Output from AI:**
```json
{
  "reasoning": "I need to search for cat photos. First, I should click on the search box.",
  "action": {
    "type": "click",
    "target": "search_box"
  },
  "memory_update": "Clicked on search box to start searching"
}
```

## 🎓 Learning Path

1. **Start with `examples/simple_demo.py`** - Understand the basic flow
2. **Read `src/simple_ai_agent/agent.py`** - See the core logic
3. **Try `examples/real_llm_demo.py`** - Experience real AI responses
4. **Modify the actions** - Add your own action types
5. **Extend the state** - Add more context for the AI

## 🔌 Extending the Agent

### Add New Actions
```python
class ActionType(Enum):
    SEARCH = "search"
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"  # New action
    DONE = "done"
```

### Add More State
```python
@dataclass
class AgentState:
    goal: str
    current_page: str
    available_elements: List[str]
    memory: List[str]
    user_preferences: Dict[str, str]  # New field
    step_count: int = 0
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

This project is inspired by the excellent [browser-use](https://github.com/gregpr07/browser-use) project, simplified for educational purposes.
