"""
Web simulator that generates realistic webpage content for the AI agent to interact with.
This simulates what browser-use sees when it takes screenshots and parses DOM.
"""

import random
from typing import Dict, List, Optional
from .models import WebElement, PageState


class WebSimulator:
    """Simulates realistic web pages with dynamic content"""
    
    def __init__(self):
        self.current_url = "https://example.com"
        self.pages = self._initialize_pages()
        
    def _initialize_pages(self) -> Dict[str, PageState]:
        """Initialize different page types with realistic content"""
        return {
            "homepage": self._create_homepage(),
            "search_results": self._create_search_results(),
            "product_page": self._create_product_page(),
            "booking_form": self._create_booking_form(),
            "checkout": self._create_checkout_page(),
        }
    
    def _create_homepage(self) -> PageState:
        """Create a realistic homepage"""
        elements = [
            WebElement(0, "input", "Search for anything...", {"type": "search", "placeholder": "Search for anything..."}),
            WebElement(1, "button", "Search", {"class": "search-btn"}),
            WebElement(2, "a", "Sign In", {"href": "/login", "class": "login-link"}),
            WebElement(3, "a", "Shop Now", {"href": "/shop", "class": "cta-button"}),
            WebElement(4, "a", "Book Travel", {"href": "/travel", "class": "nav-link"}),
            WebElement(5, "a", "Electronics", {"href": "/electronics", "class": "category"}),
            WebElement(6, "a", "Clothing", {"href": "/clothing", "class": "category"}),
            WebElement(7, "button", "Subscribe to Newsletter", {"class": "newsletter-btn"}),
        ]
        
        page_text = """
        Welcome to ExampleShop - Your One-Stop Destination
        
        Featured Categories:
        - Electronics: Latest gadgets and tech
        - Clothing: Fashion for everyone
        - Travel: Book flights and hotels
        - Home & Garden: Everything for your home
        
        Special Offers:
        - 50% off electronics this week
        - Free shipping on orders over $50
        - New customer discount: 20% off first order
        """
        
        return PageState(
            url="https://example.com",
            title="ExampleShop - Home",
            elements=elements,
            page_text=page_text.strip()
        )
    
    def _create_search_results(self, query: str = "default") -> PageState:
        """Create search results page based on query"""
        products = [
            ("Wireless Headphones", "$99.99", "4.5 stars", "Premium sound quality"),
            ("Laptop Stand", "$29.99", "4.2 stars", "Ergonomic design"),
            ("Coffee Maker", "$149.99", "4.7 stars", "Programmable brewing"),
            ("Desk Lamp", "$39.99", "4.3 stars", "LED with dimmer"),
            ("Phone Case", "$19.99", "4.1 stars", "Drop protection"),
        ]
        
        elements = [
            WebElement(0, "input", query, {"type": "search", "value": query}),
            WebElement(1, "button", "Search", {"class": "search-btn"}),
            WebElement(2, "select", "Sort by", {"options": "relevance,price-low,price-high,rating"}),
        ]
        
        # Add product elements
        for i, (name, price, rating, desc) in enumerate(products):
            base_idx = 3 + (i * 4)
            elements.extend([
                WebElement(base_idx, "h3", name, {"class": "product-title"}),
                WebElement(base_idx + 1, "span", price, {"class": "price"}),
                WebElement(base_idx + 2, "span", rating, {"class": "rating"}),
                WebElement(base_idx + 3, "button", "Add to Cart", {"class": "add-cart", "data-product": name}),
            ])
        
        page_text = f"""
        Search Results for "{query}" - {len(products)} items found
        
        Products:
        """ + "\n".join([f"- {name}: {price} ({rating}) - {desc}" for name, price, rating, desc in products])
        
        return PageState(
            url=f"https://example.com/search?q={query}",
            title=f"Search: {query}",
            elements=elements,
            page_text=page_text.strip()
        )
    
    def _create_product_page(self, product_name: str = "Wireless Headphones") -> PageState:
        """Create a detailed product page"""
        elements = [
            WebElement(0, "h1", product_name, {"class": "product-title"}),
            WebElement(1, "span", "$99.99", {"class": "price"}),
            WebElement(2, "span", "4.5 stars (234 reviews)", {"class": "rating"}),
            WebElement(3, "select", "Color", {"options": "black,white,blue,red"}),
            WebElement(4, "select", "Quantity", {"options": "1,2,3,4,5"}),
            WebElement(5, "button", "Add to Cart", {"class": "add-cart-btn"}),
            WebElement(6, "button", "Buy Now", {"class": "buy-now-btn"}),
            WebElement(7, "a", "Read Reviews", {"href": "#reviews"}),
            WebElement(8, "a", "Shipping Info", {"href": "#shipping"}),
            WebElement(9, "button", "Add to Wishlist", {"class": "wishlist-btn"}),
        ]
        
        page_text = f"""
        {product_name}
        Price: $99.99
        Rating: 4.5 stars (234 reviews)
        
        Product Description:
        Premium wireless headphones with noise cancellation, 30-hour battery life,
        and superior sound quality. Perfect for music lovers and professionals.
        
        Features:
        - Active noise cancellation
        - 30-hour battery life
        - Bluetooth 5.0 connectivity
        - Comfortable over-ear design
        - Quick charge: 15 min = 3 hours playback
        
        Customer Reviews:
        "Amazing sound quality!" - Sarah M. ⭐⭐⭐⭐⭐
        "Great for long flights" - John D. ⭐⭐⭐⭐⭐
        "Good value for money" - Lisa K. ⭐⭐⭐⭐
        """
        
        return PageState(
            url=f"https://example.com/product/{product_name.lower().replace(' ', '-')}",
            title=f"{product_name} - ExampleShop",
            elements=elements,
            page_text=page_text.strip()
        )
    
    def _create_booking_form(self) -> PageState:
        """Create a travel booking form"""
        elements = [
            WebElement(0, "input", "", {"type": "text", "placeholder": "From (departure city)"}),
            WebElement(1, "input", "", {"type": "text", "placeholder": "To (destination)"}),
            WebElement(2, "input", "", {"type": "date", "name": "departure"}),
            WebElement(3, "input", "", {"type": "date", "name": "return"}),
            WebElement(4, "select", "Passengers", {"options": "1,2,3,4,5,6+"}),
            WebElement(5, "select", "Class", {"options": "economy,premium,business,first"}),
            WebElement(6, "button", "Search Flights", {"class": "search-flights-btn"}),
            WebElement(7, "a", "Hotels", {"href": "/hotels"}),
            WebElement(8, "a", "Car Rentals", {"href": "/cars"}),
        ]
        
        page_text = """
        Book Your Flight
        
        Find the best deals on flights worldwide
        
        Popular Destinations:
        - New York to London: from $299
        - Los Angeles to Tokyo: from $599
        - Miami to Paris: from $399
        - Chicago to Rome: from $449
        
        Why Book With Us:
        ✓ Best price guarantee
        ✓ 24/7 customer support
        ✓ Free cancellation on most bookings
        ✓ Instant confirmation
        """
        
        return PageState(
            url="https://example.com/flights",
            title="Book Flights - ExampleTravel",
            elements=elements,
            page_text=page_text.strip()
        )
    
    def _create_checkout_page(self) -> PageState:
        """Create a checkout page"""
        elements = [
            WebElement(0, "input", "", {"type": "email", "placeholder": "Email address"}),
            WebElement(1, "input", "", {"type": "text", "placeholder": "First name"}),
            WebElement(2, "input", "", {"type": "text", "placeholder": "Last name"}),
            WebElement(3, "input", "", {"type": "text", "placeholder": "Address"}),
            WebElement(4, "input", "", {"type": "text", "placeholder": "City"}),
            WebElement(5, "select", "State", {"options": "CA,NY,TX,FL,WA"}),
            WebElement(6, "input", "", {"type": "text", "placeholder": "ZIP code"}),
            WebElement(7, "input", "", {"type": "text", "placeholder": "Card number"}),
            WebElement(8, "input", "", {"type": "text", "placeholder": "MM/YY"}),
            WebElement(9, "input", "", {"type": "text", "placeholder": "CVV"}),
            WebElement(10, "button", "Place Order", {"class": "place-order-btn"}),
        ]
        
        page_text = """
        Checkout
        
        Order Summary:
        - Wireless Headphones x1: $99.99
        - Shipping: $9.99
        - Tax: $8.25
        Total: $118.23
        
        Shipping Information:
        Please provide your shipping address
        
        Payment Information:
        We accept Visa, MasterCard, American Express
        """
        
        return PageState(
            url="https://example.com/checkout",
            title="Checkout - ExampleShop",
            elements=elements,
            page_text=page_text.strip()
        )
    
    def get_current_page(self) -> PageState:
        """Get the current page state"""
        # Determine page type from URL
        if "search" in self.current_url:
            query = self.current_url.split("q=")[-1] if "q=" in self.current_url else "default"
            return self._create_search_results(query)
        elif "product" in self.current_url:
            product = self.current_url.split("/")[-1].replace("-", " ").title()
            return self._create_product_page(product)
        elif "flights" in self.current_url:
            return self.pages["booking_form"]
        elif "checkout" in self.current_url:
            return self.pages["checkout"]
        else:
            return self.pages["homepage"]
    
    def navigate_to(self, url: str) -> PageState:
        """Navigate to a new URL"""
        self.current_url = url
        return self.get_current_page()
    
    def click_element(self, index: int) -> str:
        """Simulate clicking an element"""
        page = self.get_current_page()
        element = page.get_element_by_index(index)
        
        if not element:
            return f"Error: Element [{index}] not found"
        
        # Simulate different click behaviors
        if element.tag == "a" and element.attributes and "href" in element.attributes:
            href = element.attributes["href"]
            if href.startswith("/"):
                self.current_url = f"https://example.com{href}"
            else:
                self.current_url = href
            return f"Navigated to {self.current_url}"
        
        elif element.tag == "button":
            if "search" in element.text.lower():
                self.current_url = "https://example.com/search?q=default"
                return "Search executed"
            elif "add to cart" in element.text.lower():
                return f"Added item to cart"
            elif "buy now" in element.text.lower():
                self.current_url = "https://example.com/checkout"
                return "Proceeding to checkout"
            else:
                return f"Clicked button: {element.text}"
        
        return f"Clicked element [{index}]: {element.text}"
    
    def type_text(self, index: int, text: str) -> str:
        """Simulate typing text into an element"""
        page = self.get_current_page()
        element = page.get_element_by_index(index)
        
        if not element:
            return f"Error: Element [{index}] not found"
        
        if element.tag != "input":
            return f"Error: Cannot type into {element.tag} element"
        
        # Update the element's value
        if element.attributes:
            element.attributes["value"] = text
        
        return f"Typed '{text}' into {element.text or 'input field'}"
    
    def scroll(self, direction: str) -> str:
        """Simulate scrolling"""
        return f"Scrolled {direction}"
