"""
LLM client implementations for different providers.
"""

import json
import logging
import os
from abc import ABC, abstractmethod
from typing import Dict, List

logger = logging.getLogger(__name__)


class LLMClient(ABC):
    """Abstract base class for LLM clients"""
    
    @abstractmethod
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Call the LLM with messages and return the response"""
        pass


class SimulatedLLMClient(LLMClient):
    """Simulated LLM client that returns hardcoded responses"""
    
    def __init__(self, goal: str):
        self.goal = goal.lower()
        self.call_count = 0
    
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Simulate LLM responses based on the goal and call count"""
        # Log the input
        logger.info("🔄 LLM Input:")
        for i, msg in enumerate(messages):
            content_preview = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
            logger.info(f"  [{i}] {msg['role']}: {content_preview}")
        
        # Generate response based on goal and step
        if "search for cat photos" in self.goal or "cat photos" in self.goal:
            responses = [
                {
                    "reasoning": "I can see a search input field at index 0. I should click on it first to focus it, then type my search query.",
                    "action": {"type": "click_element", "index": 0},
                    "memory_update": "Clicked on search input to focus it"
                },
                {
                    "reasoning": "Now I'll type 'cat photos' into the focused search field.",
                    "action": {"type": "type_text", "index": 0, "text": "cat photos"},
                    "memory_update": "Typed 'cat photos' into search field"
                },
                {
                    "reasoning": "I can see a search button at index 1. I'll click it to execute the search.",
                    "action": {"type": "click_element", "index": 1},
                    "memory_update": "Clicked search button to find cat photos"
                },
                {
                    "reasoning": "Perfect! I can see search results with various products. The search for cat photos has been completed successfully.",
                    "action": {"type": "done"},
                    "memory_update": "Successfully found cat photos in search results"
                }
            ]
        elif "book" in self.goal.lower() and "flight" in self.goal.lower():
            responses = [
                {
                    "reasoning": "I need to book a flight. I can see this looks like a travel booking page. Let me click on 'Book Travel' to access flight booking.",
                    "action": {"type": "click_element", "index": 4},
                    "memory_update": "Navigated to travel booking section"
                },
                {
                    "reasoning": "Great! Now I'm on a flight booking page. I can see departure city input at index 0. Let me fill that in first.",
                    "action": {"type": "type_text", "index": 0, "text": "New York"},
                    "memory_update": "Entered departure city: New York"
                },
                {
                    "reasoning": "Now I'll fill in the destination city in the 'To' field at index 1.",
                    "action": {"type": "type_text", "index": 1, "text": "Paris"},
                    "memory_update": "Entered destination: Paris"
                },
                {
                    "reasoning": "I can see a 'Search Flights' button at index 6. Let me click it to search for available flights.",
                    "action": {"type": "click_element", "index": 6},
                    "memory_update": "Searched for flights from New York to Paris"
                },
                {
                    "reasoning": "Flight search has been initiated. The booking process is underway.",
                    "action": {"type": "done"},
                    "memory_update": "Flight booking process completed"
                }
            ]
        elif "shop" in self.goal.lower() or "buy" in self.goal.lower():
            responses = [
                {
                    "reasoning": "I need to shop for something. I can see a 'Shop Now' link at index 3. Let me click on it.",
                    "action": {"type": "click_element", "index": 3},
                    "memory_update": "Navigated to shopping section"
                },
                {
                    "reasoning": "I'm now in the shopping area. Let me search for what I need using the search box.",
                    "action": {"type": "click_element", "index": 0},
                    "memory_update": "Clicked on search box to start shopping"
                },
                {
                    "reasoning": "Shopping task has been initiated successfully.",
                    "action": {"type": "done"},
                    "memory_update": "Shopping process started"
                }
            ]
        else:
            # Default responses for other goals
            responses = [
                {
                    "reasoning": f"I need to work on the goal: {self.goal}. I can see several options on this page. Let me start by using the search functionality to find what I need.",
                    "action": {"type": "click_element", "index": 0},
                    "memory_update": f"Started working on goal: {self.goal} by accessing search"
                },
                {
                    "reasoning": "Let me search for something related to my goal.",
                    "action": {"type": "type_text", "index": 0, "text": self.goal.split()[-1] if self.goal.split() else "help"},
                    "memory_update": "Entered search term related to my goal"
                },
                {
                    "reasoning": "I've made progress on the goal. Marking as complete.",
                    "action": {"type": "done"},
                    "memory_update": "Completed the task to the best of my ability"
                }
            ]
        
        # Get the appropriate response
        if self.call_count < len(responses):
            response = responses[self.call_count]
        else:
            response = {
                "reasoning": "I have completed all the steps I can think of.",
                "action": {"type": "done"},
                "memory_update": "Task completed"
            }
        
        self.call_count += 1
        response_json = json.dumps(response, indent=2)
        
        # Log the output
        logger.info("🔄 LLM Output:")
        logger.info(f"  {response_json}")
        
        return response_json


class OpenAIClient(LLMClient):
    """OpenAI LLM client"""
    
    def __init__(self, api_key: str = None, model: str = "gpt-4o-mini"):
        try:
            from openai import OpenAI
        except ImportError:
            raise ImportError("OpenAI library not installed. Run: poetry install --extras openai")
        
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key not provided and OPENAI_API_KEY environment variable not set")
        
        self.client = OpenAI(api_key=self.api_key)
        self.model = model
    
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Call OpenAI API"""
        # Log the input
        logger.info("🔄 LLM Input:")
        for i, msg in enumerate(messages):
            content_preview = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
            logger.info(f"  [{i}] {msg['role']}: {content_preview}")
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=500,
                temperature=0.1
            )
            content = response.choices[0].message.content
            
            # Log the output
            logger.info("🔄 LLM Output:")
            logger.info(f"  {content}")
            
            return content
            
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            # Fallback response
            return json.dumps({
                "reasoning": "OpenAI API call failed, using fallback",
                "action": {"type": "done"},
                "memory_update": "Error occurred during OpenAI call"
            })


class AnthropicClient(LLMClient):
    """Anthropic LLM client"""
    
    def __init__(self, api_key: str = None, model: str = "claude-3-haiku-20240307"):
        try:
            from anthropic import Anthropic
        except ImportError:
            raise ImportError("Anthropic library not installed. Run: poetry install --extras anthropic")
        
        self.api_key = api_key or os.getenv('ANTHROPIC_API_KEY')
        if not self.api_key:
            raise ValueError("Anthropic API key not provided and ANTHROPIC_API_KEY environment variable not set")
        
        self.client = Anthropic(api_key=self.api_key)
        self.model = model
    
    def call(self, messages: List[Dict[str, str]]) -> str:
        """Call Anthropic API"""
        # Log the input
        logger.info("🔄 LLM Input:")
        for i, msg in enumerate(messages):
            content_preview = msg['content'][:200] + "..." if len(msg['content']) > 200 else msg['content']
            logger.info(f"  [{i}] {msg['role']}: {content_preview}")
        
        try:
            # Separate system message from user messages for Anthropic
            system_message = None
            user_messages = []
            
            for msg in messages:
                if msg['role'] == 'system':
                    system_message = msg['content']
                else:
                    user_messages.append(msg)
            
            response = self.client.messages.create(
                model=self.model,
                max_tokens=500,
                temperature=0.1,
                system=system_message or "You are a helpful AI assistant.",
                messages=user_messages
            )
            content = response.content[0].text
            
            # Log the output
            logger.info("🔄 LLM Output:")
            logger.info(f"  {content}")
            
            return content
            
        except Exception as e:
            logger.error(f"Anthropic API call failed: {e}")
            # Fallback response
            return json.dumps({
                "reasoning": "Anthropic API call failed, using fallback",
                "action": {"type": "done"},
                "memory_update": "Error occurred during Anthropic call"
            })


def create_llm_client(client_type: str, goal: str = "", **kwargs) -> LLMClient:
    """Factory function to create LLM clients"""
    if client_type.lower() == "simulate":
        return SimulatedLLMClient(goal)
    elif client_type.lower() == "openai":
        return OpenAIClient(**kwargs)
    elif client_type.lower() == "anthropic":
        return AnthropicClient(**kwargs)
    else:
        raise ValueError(f"Unknown client type: {client_type}")
