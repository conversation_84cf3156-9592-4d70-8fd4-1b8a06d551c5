#!/usr/bin/env python3
"""
Auto Browser Demo - AI agent that automatically controls a real browser.
No user input required - runs automatically with a predefined goal.
"""

import logging
import os
import sys
import time
import json
import re
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent.browser_automation import SyncBrowserWrapper
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


class AutoBrowserAgent:
    """AI Agent that automatically controls a real browser"""
    
    def __init__(self, goal: str, llm_client, headless: bool = True):
        self.goal = goal
        self.llm_client = llm_client
        self.browser = SyncBrowserWrapper(headless=headless)
        self.step_count = 0
        self.memory = []
        self.conversation_history = []
        
    def start(self):
        """Start the browser"""
        self.browser.start()
        
    def stop(self):
        """Stop the browser"""
        self.browser.stop()
        
    def get_system_prompt(self) -> str:
        """Get the system prompt for the AI"""
        return """You are a helpful AI agent that can control a web browser to accomplish tasks.

You will be given:
1. A goal to accomplish
2. The current state of the webpage (URL, title, available elements)
3. Your memory of previous actions

You must respond with a JSON object containing:
- "reasoning": Your thought process about what to do next
- "action": The action to take (see available actions below)
- "memory_update": A brief note about what you did for future reference

Available actions:
- {"type": "click_element", "index": N} - Click on element at index N
- {"type": "type_text", "index": N, "text": "..."} - Type text into element at index N
- {"type": "type_and_enter", "index": N, "text": "..."} - Type text and press Enter
- {"type": "navigate", "url": "..."} - Navigate to a specific URL
- {"type": "scroll", "direction": "up|down"} - Scroll the page
- {"type": "extract_text", "index": N} - Extract text from element at index N
- {"type": "done"} - Mark the task as complete

Be methodical and explain your reasoning clearly. Focus on accomplishing the goal efficiently."""

    def get_current_state_description(self) -> str:
        """Get a description of the current state"""
        page_state = self.browser.get_current_page_state()
        
        description = f"""
Current State:
- Goal: {self.goal}
- Step: {self.step_count}
- URL: {page_state.url}
- Page Title: {page_state.title}
- Memory: {'; '.join(self.memory) if self.memory else 'None'}

Available Elements:
"""
        
        for element in page_state.elements[:15]:  # Limit to first 15 elements
            description += f"{element}\n"
            
        if len(page_state.elements) > 15:
            description += f"... and {len(page_state.elements) - 15} more elements\n"
            
        description += f"\nPage Content Preview:\n{page_state.page_text[:400]}..."
        
        return description
        
    def execute_action(self, action_data: dict) -> str:
        """Execute an action and return the result"""
        action_type = action_data.get("type")
        
        if action_type == "click_element":
            index = action_data.get("index", 0)
            return self.browser.click_element(index)
            
        elif action_type == "type_text":
            index = action_data.get("index", 0)
            text = action_data.get("text", "")
            return self.browser.type_text(index, text)

        elif action_type == "type_and_enter":
            index = action_data.get("index", 0)
            text = action_data.get("text", "")
            return self.browser.type_text(index, text, press_enter=True)
            
        elif action_type == "navigate":
            url = action_data.get("url", "")
            return self.browser.navigate_to(url)
            
        elif action_type == "scroll":
            direction = action_data.get("direction", "down")
            return self.browser.scroll(direction)
            
        elif action_type == "extract_text":
            index = action_data.get("index", 0)
            return self.browser.extract_text(index)
            
        elif action_type == "done":
            return "Task marked as complete"
            
        else:
            return f"Unknown action type: {action_type}"
            
    def step(self) -> bool:
        """Execute one step of the agent. Returns True if done."""
        # Get current state
        state_description = self.get_current_state_description()
        
        # Prepare messages for the AI
        messages = [
            {"role": "system", "content": self.get_system_prompt()},
            {"role": "user", "content": state_description}
        ]
        
        # Add conversation history (keep it short)
        if len(self.conversation_history) > 6:  # Keep only last 3 exchanges
            self.conversation_history = self.conversation_history[-6:]
        messages.extend(self.conversation_history)
        
        print(f"\n🤔 Asking AI what to do next...")
        
        # Call the AI
        response = self.llm_client.call(messages)
        
        # Parse the response
        try:
            # Clean up the response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()
            
            # Remove comments
            response = re.sub(r'//.*$', '', response, flags=re.MULTILINE)
            response = response.strip()
            
            # Try to fix common JSON issues
            if not response.endswith('}'):
                response += '}'
                
            parsed_response = json.loads(response)
            
        except json.JSONDecodeError as e:
            print(f"❌ Failed to parse AI response: {e}")
            print(f"Raw response: {response}")
            # Fallback to a safe action
            parsed_response = {
                "reasoning": "Failed to parse response",
                "action": {"type": "done"},
                "memory_update": "Error occurred"
            }
            
        # Extract components
        reasoning = parsed_response.get("reasoning", "No reasoning provided")
        action = parsed_response.get("action", {"type": "done"})
        memory_update = parsed_response.get("memory_update", "")
        
        # Log the AI's reasoning
        print(f"\n💭 AI Reasoning: {reasoning}")
        print(f"🎯 Action: {action}")
        
        # Execute the action
        result = self.execute_action(action)
        print(f"✅ Result: {result}")
        
        # Update memory
        if memory_update:
            self.memory.append(memory_update)
            
        # Update conversation history
        self.conversation_history.append({"role": "assistant", "content": json.dumps(parsed_response)})
        self.conversation_history.append({"role": "user", "content": f"Action result: {result}"})
        
        # Increment step count
        self.step_count += 1
        
        # Check if done
        return action.get("type") == "done"
        
    def run(self, max_steps: int = 8, start_url: str = "https://www.google.com"):
        """Run the agent for up to max_steps"""
        print(f"🚀 Starting AI browser agent")
        print(f"🎯 Goal: {self.goal}")
        print(f"🌐 Starting URL: {start_url}")
        
        try:
            # Navigate to starting URL
            self.browser.navigate_to(start_url)
            
            for step in range(max_steps):
                print(f"\n{'='*60}")
                print(f"Step {step + 1}/{max_steps}")
                print(f"{'='*60}")
                
                is_done = self.step()
                
                if is_done:
                    print(f"\n🎉 Task completed in {step + 1} steps!")
                    break
                    
                # Longer delay so you can see what's happening
                print("⏳ Waiting 3 seconds before next step...")
                time.sleep(3)
                
            else:
                print(f"\n⏰ Reached maximum steps ({max_steps})")
                
        finally:
            self.stop()


def main():
    """Run the auto browser demo"""
    setup_logging()
    
    # Load environment variables from .env file
    load_dotenv()
    
    print("🤖 AI Browser Agent - Auto Demo")
    print("=" * 40)
    print("This demo uses a REAL browser controlled by AI!")
    print("The AI will automatically navigate and interact with websites.")
    print("=" * 40)
    
    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return
        
    # Predefined goal
    goal = "Search for 'AI agents' on Google and click on the first result"

    print(f"\n🎯 Goal: {goal}")
    print("🌐 Opening visible browser window - you'll see the AI control it!")
    print("👀 Watch as the AI navigates, types, and clicks automatically...")

    # Create LLM client
    llm_client = create_llm_client(model="gpt-4o-mini")

    # Create and run agent with visible browser
    agent = AutoBrowserAgent(goal, llm_client, headless=False)
    
    try:
        agent.start()
        agent.run(max_steps=6)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n👋 Demo completed!")


if __name__ == "__main__":
    main()
