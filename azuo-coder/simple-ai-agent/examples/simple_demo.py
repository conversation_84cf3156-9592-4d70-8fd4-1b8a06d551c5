#!/usr/bin/env python3
"""
Simple demo using OpenAI.
Requires OPENAI_API_KEY environment variable.
"""

import logging
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Add the src directory to the path so we can import our package
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from simple_ai_agent import SimpleAIAgent
from simple_ai_agent.llm_clients import create_llm_client


def setup_logging():
    """Setup logging for the demo"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )


def main():
    """Run the simple demo"""
    setup_logging()

    # Load environment variables from .env file
    load_dotenv()

    print("🤖 Simple AI Agent Demo")
    print("=" * 50)
    print("This demo uses OpenAI for real AI reasoning.")
    print("Make sure you have OPENAI_API_KEY set in your .env file.")
    print("=" * 50)

    # Check for API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY not found")
        print("Please add your OpenAI API key to the .env file:")
        print("   OPENAI_API_KEY=your-key-here")
        return

    # Create OpenAI LLM client
    goal = "Search for cat photos"
    llm_client = create_llm_client(model="gpt-4o-mini")

    # Create and run the agent
    agent = SimpleAIAgent(goal, llm_client)
    
    try:
        agent.run(max_steps=5)
        
        # Show final state
        print("\n" + "=" * 50)
        print("AGENT STATE SUMMARY")
        print("=" * 50)
        state = agent.get_state()
        print(f"Goal: {state.goal}")
        print(f"Steps taken: {state.step_count}")
        print(f"Current page: {state.current_page}")
        print(f"Available elements: {len(state.current_page.elements)} elements")
        print(f"Memory entries: {len(state.memory)}")
        for i, memory in enumerate(state.memory, 1):
            print(f"  {i}. {memory}")
        
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Error during demo: {e}")


if __name__ == "__main__":
    main()
