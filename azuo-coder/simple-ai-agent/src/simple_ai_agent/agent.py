"""
Core agent implementation for the Simple AI Agent.
"""

import json
import logging
from typing import Any, Dict, List

from .llm_clients import LLMClient
from .models import Action, ActionType, AgentState
from .web_simulator import WebSimulator

logger = logging.getLogger(__name__)


class SimpleAIAgent:
    """A simple AI agent that demonstrates planning and reasoning with dynamic web content"""

    def __init__(self, goal: str, llm_client: LLMClient):
        self.goal = goal
        self.llm_client = llm_client
        self.web_simulator = WebSimulator()

        # Initialize with current page state
        current_page = self.web_simulator.get_current_page()
        self.state = AgentState(
            goal=goal,
            current_page=current_page,
            memory=[]
        )
        self.conversation_history = []
        
    def get_system_prompt(self) -> str:
        """System prompt that tells the AI how to behave"""
        return """You are a helpful AI agent that can interact with websites by analyzing their content and taking actions.

You can see the current webpage content including:
- URL and page title
- All interactive elements with their indices, types, and text
- Visible text content on the page

Available actions:
- click_element: Click on any element by its index number
- type_text: Type text into input fields (specify index and text)
- scroll: Scroll the page (up/down)
- navigate: Go to a specific URL
- extract_text: Extract text from an element
- done: Mark the task as completed

You should respond with a JSON object containing:
{
    "reasoning": "Your thought process about what you see and what to do next",
    "action": {
        "type": "click_element|type_text|scroll|navigate|extract_text|done",
        "index": 5,  // for click_element, type_text, extract_text
        "text": "text to type",  // for type_text
        "url": "https://example.com",  // for navigate
        "direction": "down"  // for scroll
    },
    "memory_update": "What you want to remember about this step"
}

Analyze the page content carefully and choose the most appropriate action based on what you can see.
Always respond with valid JSON."""

    def get_current_state_description(self) -> str:
        """Describe the current state for the AI"""
        page = self.state.current_page
        return f"""
Current Goal: {self.state.goal}
Current URL: {page.url}
Page Title: {page.title}
Step: {self.state.step_count}

Available Elements:
{page.get_elements_summary()}

Page Content:
{page.page_text[:1000]}{"..." if len(page.page_text) > 1000 else ""}

Memory: {self.state.get_memory_summary()}
"""

    def parse_ai_response(self, response: str) -> Dict[str, Any]:
        """Parse the AI's JSON response"""
        try:
            # Try to extract JSON from the response
            response = response.strip()
            if response.startswith('```json'):
                response = response[7:]
            if response.endswith('```'):
                response = response[:-3]
            response = response.strip()

            # Remove JavaScript-style comments that sometimes appear in AI responses
            import re
            response = re.sub(r'//.*$', '', response, flags=re.MULTILINE)
            response = response.strip()

            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI response: {e}")
            logger.error(f"Raw response: {response}")
            return {
                "reasoning": "Failed to parse response",
                "action": {"type": "done"},
                "memory_update": "Error occurred"
            }

    def execute_action(self, action_data: Dict[str, Any]) -> str:
        """Execute the action and return the result"""
        action_type = action_data.get("type")
        index = action_data.get("index")
        text = action_data.get("text")
        url = action_data.get("url")
        direction = action_data.get("direction")

        try:
            action = Action(
                type=ActionType(action_type),
                index=index,
                text=text,
                url=url,
                direction=direction
            )
        except ValueError:
            return f"Error: Unknown action type '{action_type}'"

        logger.info(f"🎯 Executing Action: {action}")

        # Execute action using web simulator
        if action.type == ActionType.CLICK_ELEMENT:
            if action.index is None:
                return "Error: No element index specified for click"
            result = self.web_simulator.click_element(action.index)
            # Update current page state after action
            self.state.current_page = self.web_simulator.get_current_page()
            return result

        elif action.type == ActionType.TYPE_TEXT:
            if action.index is None or action.text is None:
                return "Error: Element index and text required for typing"
            result = self.web_simulator.type_text(action.index, action.text)
            # Update current page state after action
            self.state.current_page = self.web_simulator.get_current_page()
            return result

        elif action.type == ActionType.SCROLL:
            direction = action.direction or "down"
            result = self.web_simulator.scroll(direction)
            return result

        elif action.type == ActionType.NAVIGATE:
            if action.url is None:
                return "Error: URL required for navigation"
            self.state.current_page = self.web_simulator.navigate_to(action.url)
            return f"Navigated to {action.url}"

        elif action.type == ActionType.EXTRACT_TEXT:
            if action.index is None:
                return "Error: Element index required for text extraction"
            page = self.state.current_page
            element = page.get_element_by_index(action.index)
            if element:
                return f"Extracted text: '{element.text}'"
            else:
                return f"Error: Element [{action.index}] not found"

        elif action.type == ActionType.DONE:
            return "Task marked as completed."

        return "Unknown action executed."

    def step(self) -> bool:
        """Take one step in the agent's execution. Returns True if done."""
        logger.info(f"\n{'='*50}")
        logger.info(f"STEP {self.state.step_count + 1}")
        logger.info(f"{'='*50}")
        
        # Prepare messages for the AI
        messages = [
            {"role": "system", "content": self.get_system_prompt()},
            {"role": "user", "content": self.get_current_state_description()}
        ]
        
        # Add conversation history
        for msg in self.conversation_history:
            messages.append(msg)
        
        # Call the AI
        logger.info("📥 Calling AI for next action...")
        ai_response = self.llm_client.call(messages)
        
        # Parse the response
        parsed_response = self.parse_ai_response(ai_response)
        
        # Log the AI's reasoning
        logger.info(f"🧠 AI Reasoning: {parsed_response.get('reasoning', 'No reasoning provided')}")
        
        # Execute the action
        result = self.execute_action(parsed_response.get('action', {}))
        logger.info(f"📤 Action Result: {result}")
        
        # Update memory
        memory_update = parsed_response.get('memory_update')
        if memory_update:
            self.state.add_memory(memory_update)
            logger.info(f"🧠 Memory Updated: {memory_update}")
        
        # Add to conversation history
        self.conversation_history.extend([
            {"role": "assistant", "content": ai_response},
            {"role": "user", "content": f"Action result: {result}"}
        ])
        
        # Update step count
        self.state.increment_step()
        
        # Check if done
        action_type = parsed_response.get('action', {}).get('type')
        return action_type == 'done'

    def run(self, max_steps: int = 10) -> None:
        """Run the agent until completion or max steps"""
        logger.info(f"🚀 Starting AI Agent")
        logger.info(f"Goal: {self.goal}")
        
        for step_num in range(max_steps):
            try:
                is_done = self.step()
                if is_done:
                    logger.info("✅ Task completed successfully!")
                    break
            except Exception as e:
                logger.error(f"❌ Error in step {step_num + 1}: {e}")
                break
        else:
            logger.info("❌ Reached maximum steps without completion")
        
        # Final summary
        logger.info(f"\n{'='*50}")
        logger.info("FINAL SUMMARY")
        logger.info(f"{'='*50}")
        logger.info(f"Goal: {self.goal}")
        logger.info(f"Steps taken: {self.state.step_count}")
        logger.info(f"Final page: {self.state.current_page}")
        logger.info(f"Memory: {self.state.get_memory_summary()}")

    def get_state(self) -> AgentState:
        """Get the current agent state"""
        return self.state
    
    def get_conversation_history(self) -> List[Dict[str, str]]:
        """Get the conversation history"""
        return self.conversation_history.copy()
