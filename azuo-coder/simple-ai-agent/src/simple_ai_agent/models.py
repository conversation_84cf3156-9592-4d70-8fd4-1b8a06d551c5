"""
Data models for the Simple AI Agent.
"""

from dataclasses import dataclass
from enum import Enum
from typing import List, Optional, Dict, Any


class ActionType(Enum):
    """Available actions the agent can take"""
    CLICK_ELEMENT = "click_element"
    TYPE_TEXT = "type_text"
    SCROLL = "scroll"
    NAVIGATE = "navigate"
    EXTRACT_TEXT = "extract_text"
    DONE = "done"


@dataclass
class WebElement:
    """Represents an element on a webpage"""
    index: int
    tag: str
    text: Optional[str] = None
    attributes: Optional[Dict[str, str]] = None

    def __str__(self):
        attrs = []
        if self.text:
            attrs.append(f'text="{self.text[:50]}{"..." if len(self.text) > 50 else ""}"')
        if self.attributes:
            for key, value in self.attributes.items():
                if key in ['id', 'class', 'type', 'placeholder', 'href']:
                    attrs.append(f'{key}="{value}"')
        attr_str = " " + " ".join(attrs) if attrs else ""
        return f"[{self.index}] <{self.tag}{attr_str}>"


@dataclass
class PageState:
    """Current state of a webpage"""
    url: str
    title: str
    elements: List[WebElement]
    page_text: str  # Visible text content

    def get_element_by_index(self, index: int) -> Optional[WebElement]:
        """Get element by index"""
        for element in self.elements:
            if element.index == index:
                return element
        return None

    def get_elements_summary(self) -> str:
        """Get a summary of all elements"""
        if not self.elements:
            return "No interactive elements found"

        summary = []
        for element in self.elements[:10]:  # Limit to first 10 elements
            summary.append(str(element))

        if len(self.elements) > 10:
            summary.append(f"... and {len(self.elements) - 10} more elements")

        return "\n".join(summary)


@dataclass
class Action:
    """Represents an action the agent wants to take"""
    type: ActionType
    index: Optional[int] = None      # Element index to interact with
    text: Optional[str] = None       # Text to type
    url: Optional[str] = None        # URL to navigate to
    direction: Optional[str] = None  # Scroll direction

    def __str__(self):
        if self.type == ActionType.CLICK_ELEMENT:
            return f"Click element [{self.index}]"
        elif self.type == ActionType.TYPE_TEXT:
            return f"Type '{self.text}' into element [{self.index}]"
        elif self.type == ActionType.SCROLL:
            return f"Scroll {self.direction}"
        elif self.type == ActionType.NAVIGATE:
            return f"Navigate to: {self.url}"
        elif self.type == ActionType.EXTRACT_TEXT:
            return f"Extract text from element [{self.index}]"
        elif self.type == ActionType.DONE:
            return "Task completed"
        return str(self.type)


@dataclass
class AgentState:
    """Current state of the agent"""
    goal: str
    current_page: PageState
    memory: List[str]  # What the agent remembers
    step_count: int = 0

    def add_memory(self, memory: str) -> None:
        """Add a memory entry"""
        self.memory.append(memory)

    def get_memory_summary(self) -> str:
        """Get a summary of the agent's memory"""
        if not self.memory:
            return "None"
        return "; ".join(self.memory)

    def increment_step(self) -> None:
        """Increment the step counter"""
        self.step_count += 1
